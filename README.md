# Feedback Project

A modern reimplementation of the Matrix Feedback system using updated technology stacks.

## Technology Stack

### Backend (.NET 9)
- ASP.NET Core Web API
- Entity Framework Core
- SQL Server


### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd feedbackProject/backend/Service
   ```

2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```

3. Build the project:
   ```bash
   dotnet build
   ```

4. Run the application:
   ```bash
   dotnet run
   ```

### Frontend (React)
- React.js
- React Router for navigation
- Axios for API communication
- Modern UI/UX design


### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd feedbackProject/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

The application will be available at `http://localhost:3000`
