
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using MongoConfigProject;
using DataAccessLayer;
using Helper;

namespace BusinessLogicLayer
{
    public static class FileUpload
    {
        public static DocumentUrlResponse GetDocumentUrl(DocumentUrlRequest documentRequest)
        {
            var response = new DocumentUrlResponse();
            try
            {
                var headers = new Dictionary<object, object>
                {
                    { "authKey", "coreauthKey".AppSettings().ToString() },
                    { "clientKey", "coreclientKey".AppSettings().ToString() }
                };

                string coreAPIivKey = "coreAPIivKey".AppSettings().ToString();
                string coreAPIencKey = "coreAPIencKey".AppSettings().ToString();


                documentRequest.CustomerId = Crypto.Encrytion_Payment_AES(documentRequest.CustomerId, "Core", 256, 128, coreAPIencKey, coreAPIivKey, false);
                documentRequest.DocumentId = Crypto.Encrytion_Payment_AES(documentRequest.DocumentId, "Core", 256, 128, coreAPIencKey, coreAPIivKey, false);
                documentRequest.DocumentTypeId = 805;


                dynamic obj = new JObject();
                obj.custId = documentRequest.CustomerId;
                obj.docId = documentRequest.DocumentId;
                obj.docId = documentRequest.DocumentId;
                if (!string.IsNullOrEmpty(documentRequest.RefId))
                {
                    obj.RefId = documentRequest.RefId;
                }
                string jsonData = JsonConvert.SerializeObject(obj);
              
                var result = CommonAPICall.CallAPI("DocumentUploadurl".AppSettings().ToString(), jsonData, "POST", 3000,  "application/json", headers);
                response = JsonConvert.DeserializeObject<DocumentUrlResponse>(result);
            }
            catch (Exception ex)
            {

            }
            return response;
        }


        public static MiscDocUploadResponse UploadMiscDocument(MiscDocUploadRequest request, MailAttachments objMailAttachments)
        {
            MiscDocUploadResponse response = null;
            try
            {
                byte[] data = Convert.FromBase64String(objMailAttachments.AttachemntContent);
                GetConfigValue( out string endpointUrl, out string accessKey, out string secretKey, out string authKey, out string clientKey);

                var checkSumParams = new SortedList<string, string>
                {
                    { "clientKey", clientKey },
                    { "customerId", request.CustomerId.ToString() },
                    { "type", request.Type},
                    { "enquiryId", request.EnquiryId},
                    { "leadId", request.LeadId.ToString()},
                    { "productId", request.ProductId.ToString()
                    },{ "refId", request.RefId
                    }
                };
                
                var checkSum = GenerateCheckSum(checkSumParams, accessKey, secretKey);

                var payloadJSON = new
                {
                    clientKey,
                    customerId = request.CustomerId,
                    enquiryId = request.EnquiryId,
                    leadId = request.LeadId,
                    productId = request.ProductId,
                    type = request.Type,
                    refId=request.RefId,
                    signature = ComputeSha256Hash(checkSum)
                };

                var content = new ByteArrayContent(data);
                content.Headers.Add("Content-Type", objMailAttachments.ContentType);

                var multipartContent = new MultipartFormDataContent
                {
                    { content, "file", objMailAttachments.FileName},
                    { new StringContent(JsonConvert.SerializeObject(payloadJSON)), "payloadJSON" }
                };
                var headerParams = new Dictionary<string, string>
                {
                    { "authKey", authKey },
                    { "clientKey", clientKey }
                };

                var result = CommonAPICall.PostApiCallSync(endpointUrl, 3000, multipartContent, headerParams);
                if (!string.IsNullOrEmpty(result))
                    response = JsonConvert.DeserializeObject<MiscDocUploadResponse>(result);
                else
                    throw new OperationCanceledException("MiscDocUploadResponse Empty!");
            }
            catch (Exception ex)
            {

            }

            return response;
        }

        public static string GenerateCheckSum(SortedList<string, string> keyValuePairs, string accessKey, string secretKey)
        {
            var checkSum = new StringBuilder();

            if (keyValuePairs != null && keyValuePairs.Count > 0)
            {
                checkSum.Append(accessKey + "|");

                foreach (var pair in keyValuePairs)
                {
                    checkSum.Append(pair.Key + "=" + pair.Value + ";");
                }

                checkSum.Append("|" + secretKey);
            }

            return checkSum.ToString();
        }

        public static string ComputeSha256Hash(string rawData)
        {
            SHA256 sha256Hash = SHA256.Create();
            byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));
            var builder = new StringBuilder();
            for (int i = 0; i < bytes.Length; i++)
            {
                builder.Append(bytes[i].ToString("x2"));
            }
            return builder.ToString();
        }

        private static void GetConfigValue( out string endpointUrl, out string accessKey, out string secretKey, out string authKey, out string clientKey)
        {
            endpointUrl = "uploadMiscDoc".AppSettings().ToString();
            accessKey = "coreaccessKey".AppSettings().ToString();
            secretKey = "coresecretKey".AppSettings().ToString();
            authKey = "coreauthKey".AppSettings().ToString();
            clientKey = "coreclientKey".AppSettings().ToString();
        }
    }
}
