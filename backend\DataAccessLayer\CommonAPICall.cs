﻿using System;
using System.Text;
using System.Net;

namespace DataAccessLayer
{
    public class CommonAPICall
    {
        public static string GetAPICall(string URL, int timeout)
        {
            if (string.IsNullOrEmpty(URL))
                return string.Empty;

            HttpClient client = new()
            {
                Timeout = TimeSpan.FromMilliseconds(timeout)
            };
            var response = client.GetAsync(URL).Result;
            return response.Content.ReadAsStringAsync().Result;
        }
        
        public static string? CallAPI(string requestUrl, string JSONRequest, string JSONmethod, int timeout, string JSONContentType, Dictionary<object, object> Headers = null)
        {
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(requestUrl);
            request.Timeout = timeout;
            request.Method = JSONmethod;
            request.ContentType = JSONContentType;
            if (Headers != null)
            {
                foreach (var header in Headers)
                {
                    request.Headers.Add(header.Key.ToString(), header.Value.ToString());
                }
            }

            var encoding = new UTF8Encoding();
            if (JSONmethod == "POST")
            {

                byte[] bytes = encoding.GetBytes(JSONRequest);
                request.ContentLength = bytes.Length;
                using Stream requestStream = request.GetRequestStream();
                requestStream.Write(bytes, 0, bytes.Length);
            }
            WebResponse webResponse = request.GetResponse();
            using Stream webStream = webResponse.GetResponseStream();
            if (webStream != null)
            {
                using StreamReader responseReader = new StreamReader(webStream);
                string _objRespons = responseReader.ReadToEnd();
                return _objRespons;
            }
            return null;

        }
    
        public static string PostApiCallSync(string url, int timeout, MultipartFormDataContent content, Dictionary<string, string> headers)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            var client = new HttpClient()
            {
                Timeout = TimeSpan.FromMilliseconds(timeout)
            };

            if (headers != null && headers.Count > 0)
            {
                foreach (KeyValuePair<string, string> item in headers)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
            }

            var response = client.PostAsync(url, content).Result;
            return response.Content.ReadAsStringAsync().Result;
        }

    }

}