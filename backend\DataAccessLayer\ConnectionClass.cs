using System.Text;
using System.Net;
using Microsoft.Extensions.Configuration;
using Helper;

namespace DataAccessLayer
{
    public class ConnectionClass
    {
        static readonly object _obj = new();
        public static string LivesqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string Enviornment = CoreCommonMethods.GetEnvironmentVar();
            string ConnectionObj = con.GetSection("ConnectionString").GetSection(Enviornment).Value.ToString();

            return ConnectionObj;
        }

        public static string ReplicasqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string Enviornment = CoreCommonMethods.GetEnvironmentVar();
            string ConnectionObj = con.GetSection("ReplicaConnectionString").GetSection(Enviornment).Value.ToString();

            return ConnectionObj;
        }

        public static string ProductDBsqlConnection()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string Enviornment = CoreCommonMethods.GetEnvironmentVar();
            string DbConnection = con.GetSection("ProductDBConnection").GetSection(Enviornment).Value.ToString();
            return DbConnection;
        }
    }
}
