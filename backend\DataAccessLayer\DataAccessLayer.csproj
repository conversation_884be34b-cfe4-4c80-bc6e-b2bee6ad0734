﻿<Project Sdk="Microsoft.NET.Sdk">

	<ItemGroup>
		<ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
		<ProjectReference Include="..\DataHelper\DataHelper.csproj" />
		<ProjectReference Include="..\LoggingHelper\LoggingHelper.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.5" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.4" />
		<PackageReference Include="AWSSDK.SecretsManager" Version="3.7.302.34" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="MongoDB.Driver" Version="2.24.0" />
	</ItemGroup>

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

</Project>
