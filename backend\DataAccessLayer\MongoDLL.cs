using PropertyLayers;
using MongoDB.Driver;
using MongoDB.Bson;

namespace DataAccessLayer
{
    public class MongoDLL
    {
        public static List<SysConfigData>? GetConfiValueFromMongo(string Source)
        {
            List<SysConfigData>? sysConfigs = null;
            try
            {
                var database = MongoSingletonClass.OneLeadDB();
                var collection = database.GetCollection<SysConfigData>(MongoCollection.ConfigValues());

                var filter = Builders<SysConfigData>.Filter.Eq("source", Source);
                sysConfigs = collection.Find(filter).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString() + "Source " + Source);
            }

            return sysConfigs;
        }

        public static Dictionary<string, SysConfigData>? GetConfiValueFromMongo()
        {
            Dictionary<string, SysConfigData>? sysConfigs = null;
            try
            {
                var database = MongoSingletonClass.OneLeadDB();
                var collection = database.GetCollection<SysConfigData>(MongoCollection.ConfigValues());

                var projection = Builders<SysConfigData>.Projection
                    .Include(x => x.source)
                    .Include(x => x.authKey)
                    .Include(x => x.clientKey)
                    .Include(x => x.EncKey)
                    .Include(x => x.EncIV);

                var data = collection.Find(FilterDefinition<SysConfigData>.Empty)
                    .Project<SysConfigData>(projection)
                    .ToList();

                sysConfigs = data.GroupBy(data => data.source.ToLower())
                    .ToDictionary(group => group.Key, group => group.First());
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString());
            }

            return sysConfigs;
        }

        public static bool IsValidateCustomer(string EncryptLeadId, string Token)
        {
            try
            {
                var database = MongoSingletonClass.OneLeadDB();
                var collection = database.GetCollection<BsonDocument>(MongoCollection.CustomerAuthenticate());

                var filter = Builders<BsonDocument>.Filter.And(
                    Builders<BsonDocument>.Filter.Eq("encryptLeadId", EncryptLeadId),
                    Builders<BsonDocument>.Filter.Eq("Token", Convert.ToInt64(Token))
                );

                var projection = Builders<BsonDocument>.Projection
                    .Include("encryptLeadId")
                    .Include("Token");

                var value = collection.Find(filter)
                    .Project<BsonDocument>(projection)
                    .FirstOrDefault();

                return value != null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in IsValidateCustomer: {ex}");
                return false;
            }
        }

        public static List<ACLConfigData> GetACLMongoConfig(ProjectionDefinition<ACLConfigData> projection, FilterDefinition<ACLConfigData> filter)
        {
            try
            {
                var database = MongoSingletonClass.OneLeadDB();
                var collection = database.GetCollection<ACLConfigData>(MongoCollection.ACLConfigValues());

                return collection.Find(filter)
                    .Project<ACLConfigData>(projection)
                    .ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GetACLMongoConfig: {ex}");
                return new List<ACLConfigData>();
            }
        }

        public static ACLConfigData? FindACLMethod(string Method)
        {
            try
            {
                var database = MongoSingletonClass.OneLeadDB();
                var collection = database.GetCollection<ACLConfigData>(MongoCollection.ACLConfigValues());

                var filter = Builders<ACLConfigData>.Filter.And(
                    Builders<ACLConfigData>.Filter.Eq(x => x.method, Method),
                    Builders<ACLConfigData>.Filter.Eq(x => x.isActive, true)
                );

                return collection.Find(filter).FirstOrDefault();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in FindACLMethod: {ex}");
                return null;
            }
        }
    }
}