using System;
using MongoDB.Driver;
using Helper;
using Microsoft.Extensions.Configuration;

namespace DataAccessLayer
{
    public class MongoSingletonClass
    {
        static IMongoDatabase? _OneLeadDB;
        private MongoSingletonClass()
        {
        }

        public static IMongoDatabase OneLeadDB()
        {
            if (_OneLeadDB != null)
            {
                return _OneLeadDB;
            }
            else
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();
                string OneLeadDB = con.GetSection("MongoDB").GetSection("OneLeadDB").Value.ToString();
                var connectionString = con.GetSection("MongoDB").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();

                var client = new MongoClient(connectionString);
                _OneLeadDB = client.GetDatabase(OneLeadDB);
                return _OneLeadDB;
            }
        }
    }
}