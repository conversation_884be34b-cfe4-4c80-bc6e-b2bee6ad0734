using Newtonsoft.Json;
using PropertyLayers;
using DataHelper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using LoggingHelper;

namespace DataAccessLayer
{
    public class PredictiveAgentStatusRedis
    {
        #region Private Methods
        private static string GetPredictiveAgentKey(string Collection, string Id)
        {
            string Key = string.Empty;
            try
            {
                if (!(string.IsNullOrEmpty(Id) && string.IsNullOrEmpty(Collection)))
                {
                    Key = $"{Collection?.Trim()}:{Id?.Trim()}";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(Id), ex.ToString(), "GetPredictiveAgentKey", "MatrixFeedback", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return Key;
        }

        #endregion

        #region Public Methods
        public static string GetMatrixToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = GetPredictiveAgentKey(RedisCollection.PredictiveAsteriskToken(), AgentId);
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetMatrixToken", "MatrixFeedback", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static string GetAppToken(string AgentId)
        {
            string result = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(AgentId))
                {
                    string Key = $"{RedisCollection.AppKey()}:{AgentId.Trim()}";
                    result = RedisHelper.GetRedisData(Key);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt32(AgentId), ex.ToString(), "GetAppToken", "MatrixFeedback", "PredictiveAgentStatusRedis", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        #endregion
    }
}
