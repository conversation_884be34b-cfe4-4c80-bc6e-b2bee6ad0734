{"format": 1, "restore": {"D:\\pb\\New folder\\matrixfeedback\\backend\\DataAccessLayer\\DataAccessLayer.csproj": {}}, "projects": {"D:\\pb\\New folder\\matrixfeedback\\backend\\DataAccessLayer\\DataAccessLayer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataAccessLayer\\DataAccessLayer.csproj", "projectName": "DataAccessLayer", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataAccessLayer\\DataAccessLayer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataAccessLayer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj"}, "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\LoggingHelper.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\LoggingHelper.csproj"}, "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.SecretsManager": {"target": "Package", "version": "[3.7.302.34, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.24.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj", "projectName": "DataHelper", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.12.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.37, )"}, "mongocsharpdriver": {"target": "Package", "version": "[2.12.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\Kafka.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\Kafka.csproj", "projectName": "Kafka", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\Kafka.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj"}, "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Confluent.Kafka": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\LoggingHelper.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\LoggingHelper.csproj", "projectName": "LoggingHelper", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\LoggingHelper.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\LoggingHelper\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\DataHelper\\DataHelper.csproj"}, "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\Kafka.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\kafka\\Kafka.csproj"}, "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj": {"projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.24.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "projectName": "PropertyLayers", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MongoDB.Driver": {"target": "Package", "version": "[2.12.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}