﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="9.0.4" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
		<PackageReference Include="mongocsharpdriver" Version="2.12.3" />
		<PackageReference Include="MongoDB.Driver" Version="2.12.3" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.37" />
	</ItemGroup>

</Project>
