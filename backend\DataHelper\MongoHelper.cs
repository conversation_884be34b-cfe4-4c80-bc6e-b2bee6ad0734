using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using MongoDB.Driver.Core.Connections;

namespace DataHelper
{
    public class MongoHelper
    {
        MongoDatabase MongoDB;
        int _mongotimeout = 0;
        public ConnectionId ConnectionId { get; }
        public MongoHelper(MongoDatabase db, int mongotimeout = 0)
        {
            MongoDB = db;
            this._mongotimeout = mongotimeout;
            if (_mongotimeout == 0)
                _mongotimeout = Convert.ToInt32(10000);
        }
        public int InsertData<T>(T objData, string CollectionTable)
        {
            Thread t = new Thread(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    var objresult = EmailCollection.Insert(objData);
                }
                catch (Exception ex)
                {
                    LogIntoFile(objData, ex.ToString(), "SaveData", "MongoHelper");
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("Error in InsertData " + e.ToString());
                }

                throw new MongoConnectionException(ConnectionId, "mongo Timeout");
            }
            else
            {
                return 1;
            }
        }
        public int SaveData<T>(T objData, string CollectionTable)
        {
            try
            {
                Thread t = new(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                        EmailCollection.Save(objData);
                    }
                    catch (Exception ex)
                    {
                        LogIntoFile(objData, ex.ToString(), "SaveData", "MongoHelper");
                    }
                }));
                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Interrupt();
                    }
                    catch (ThreadAbortException e)
                    {
                    }
                    throw new MongoException("Mongo Timeout");
                }
                else
                {
                    return 1;
                }
            }
            catch (MongoException MCEX)
            {
                throw MCEX;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public int BulkDataInsert<T>(List<T> objData, string CollectionTable)
        {
            try
            {
                var collection = MongoDB.GetCollection<T>(CollectionTable);
                collection.InsertBatch(objData);
                return 1;
            }
            catch (MongoException MCEX)
            {
                throw MCEX;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public List<T> FindAllDocument<T>(string CollectionTable) where T : class
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                MongoCursor<T> cursor = EmailCollection.FindAllAs<T>();
                return cursor.ToList();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public T FindandModify<T>(FindAndModifyArgs UpdateArgs, string CollectionTable)
        {
            FindAndModifyResult objresult = null;
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection(CollectionTable);
                    objresult = EmailCollection.FindAndModify(UpdateArgs);
                }
                catch (Exception ex)
                {

                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                }
                throw new MongoException("Mongo Timeout");
            }
            else
            {
                if (objresult == null)
                    return default;
                else
                    return objresult.GetModifiedDocumentAs<T>();
            }
        }
        public int UpdateDocument<T>(IMongoQuery query, IMongoUpdate update, string CollectionTable, UpdateFlags uf = UpdateFlags.None)
        {
            WriteConcernResult? objresult = null;
            try
            {
                Thread t = new(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection(CollectionTable);
                        objresult = EmailCollection.Update(query, update, uf);
                    }
                    catch (Exception ex)
                    {

                    }
                }));

                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Interrupt();
                    }
                    catch (ThreadAbortException e)
                    {
                    }
                    return 0;
                }
                else
                {
                    // if (objresult != null && objresult.Ok == true && objresult.DocumentsAffected > 0)
                    if (objresult != null && objresult.DocumentsAffected > 0)
                        return 1;
                    else
                        return 0;
                }
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public int Delete_ColectionByQyery<T>(IMongoQuery query, string CollectionTable)
        {
            try
            {
                var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                var r = EmailCollection.Remove(query);
                return 1;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public void RemoveDocuments(IMongoQuery query, string CollectionTable)
        {
            var EmailCollection = MongoDB.GetCollection(CollectionTable);
            EmailCollection.Remove(query);
        }
        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, IMongoSortBy strOrderField, int limit) where T : class
        {

            List<T> objlist = [];
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable).Find(query);
                    EmailCollection = EmailCollection.SetSortOrder(strOrderField);
                    if (EmailCollection != null && EmailCollection.Count() > 0)
                        objlist = EmailCollection.Take(limit).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine("GetDocument" + ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("GetDocument" + e.ToString());
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }

        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, IMongoSortBy strOrderField, IMongoFields Fields, int skip, int limit) where T : class
        {

            List<T> objlist = [];
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable).Find(query);
                    if (Fields != null)
                        EmailCollection = EmailCollection.SetFields(Fields).SetSortOrder(strOrderField);
                    else
                        EmailCollection = EmailCollection.SetSortOrder(strOrderField);
                    if (EmailCollection != null && EmailCollection.Count() > 0)
                    {
                        if (limit == 0)
                            objlist = EmailCollection.ToList();
                        else
                            objlist = EmailCollection.Skip(skip).Take(limit).ToList();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("GetDocument" + ex.ToString());

                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("GetDocument" + e.ToString());
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }
        public List<T> GetDocuments<T>(IMongoQuery query, string CollectionTable, int skip = 0, int limit = 0) where T : class
        {
            List<T> objlist = [];
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    if (limit == 0)
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).ToList();
                    else
                        objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).Skip(skip).Take(limit).ToList();
                }
                catch (Exception ex)
                {
                    Console.WriteLine("GetDocument" + ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);
            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("GetDocument" + e.ToString());
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }

        public List<T> GetDocuments<T>(IMongoQuery query, IMongoFields IncludeField, string CollectionTable) where T : class
        {
            List<T> objlist = [];

            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    objlist = MongoDB.GetCollection<T>(CollectionTable).Find(query).SetFields(IncludeField).ToList();
                }
                catch (Exception ex)
                {

                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Abort();
                }
                catch (ThreadAbortException e)
                {
                }
                return objlist;
            }
            else
            {
                return objlist;
            }
        }
        public T FindOneDocument<T>(IMongoQuery query, string CollectionTable) where T : new()
        {
            T objt = new();
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    objt = EmailCollection.FindOne(query);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error in FindOneDocument-462" + ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("Error in FindOneDocument-476" + e.ToString());
                }
                return objt;
            }
            else
            {
                return objt;
            }
        }
        public T FindOneDocument<T>(IMongoQuery query, string CollectionTable, IMongoFields Fields) where T : new()
        {

            T objt = new();
            Thread t = new(new ThreadStart(
            () =>
            {
                try
                {
                    var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                    objt = EmailCollection.FindOne(query);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error in FindOneDocument-499" + ex.ToString());
                }
            }));
            t.Start();
            t.Join(_mongotimeout);

            if (t.IsAlive)
            {
                try
                {
                    t.Interrupt();
                }
                catch (ThreadAbortException e)
                {
                    Console.WriteLine("Error in FindOneDocument-513" + e.ToString());
                }
                return objt;
            }
            else
            {
                return objt;
            }
        }
       
        public long GetDocumentCount(IMongoQuery query, string CollectionTable)
        {
            try
            {
                long DocCount = 0;
                var Collection = MongoDB.GetCollection(CollectionTable);
                DocCount = Collection.Count(query);
                return DocCount;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        public void DropCollection(string CollectionTable)
        {
            var collection = MongoDB.GetCollection(CollectionTable);
            collection.Drop();
        }
        public int SaveParserData<T>(T objData, string CollectionTable)
        {
            try
            {
                Thread t = new(new ThreadStart(
                () =>
                {
                    try
                    {
                        var EmailCollection = MongoDB.GetCollection<T>(CollectionTable);
                        EmailCollection.Save(objData);
                        string ISMongoErrorLog = "1";//"ISMongoErrorLog".AppSettings();


                        if (ISMongoErrorLog.Equals("1"))
                        {

                            LogIntoFile(objData, string.Empty, "SaveData", "MongoHelper");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogIntoFile(objData, ex.StackTrace.ToString(), "SaveData", "MongoHelper");
                    }
                }));
                t.Start();
                t.Join(_mongotimeout);

                if (t.IsAlive)
                {
                    try
                    {
                        t.Interrupt();
                    }
                    catch (ThreadAbortException e)
                    {
                        Console.WriteLine("SaveParserData" + e.ToString());
                    }
                    throw new MongoConnectionException(ConnectionId, "mongo Timeout");
                }
                else
                {
                    return 1;
                }
            }
            catch (MongoConnectionException MCEX)
            {
                throw MCEX; // Connection Exception
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private void LogIntoFile<T>(T objData, String sMsg, String MethodName, string ApplicationName)
        {
            try
            {
                string ISMongoErrorLog = "1";

                if (ISMongoErrorLog.Equals("1") || ISMongoErrorLog.Equals("2"))
                {
                    string returnString = ConvertToJsonString(objData, 0);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private static string ConvertToJsonString<T>(T objData, int length)
        {
            string returnString = string.Empty;
            if (objData != null)
            {
                returnString = objData.ToJson();
                if (length > 0)
                {
                    if (returnString.Length >= length)
                    {
                        returnString = returnString.Substring(0, length - 1);
                    }
                }
            }
            return returnString;
        }
    }
}

