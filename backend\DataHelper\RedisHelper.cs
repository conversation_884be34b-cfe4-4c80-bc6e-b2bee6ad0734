using StackExchange.Redis;
using Microsoft.Extensions.Configuration;
using Helper;

namespace DataHelper
{
    public class RedisHelper
    {
        static readonly object _obj = new();
        private static readonly List<ConnectionMultiplexer> _redisList = new();
        private static readonly int poolSize = 10;

        static IDatabase GetDatabase()
        {
            int index = new Random().Next(0, poolSize);
            ConnectionMultiplexer connection = _redisList.ElementAtOrDefault(index);

            if (_redisList == null || _redisList.Count == 0 || connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
            {
                lock (_obj)
                {
                    if (connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
                    {
                        if (connection != null)
                            _redisList.RemoveAt(index);

                        IConfiguration con = Custom.ConfigurationManager.AppSetting;

                        string Enviornment = CoreCommonMethods.GetEnvironmentVar().ToUpper();
                        List<string> nodes = con.GetSection("Redis").GetSection("CustRevisitConnection").GetSection(Enviornment).Value.Split(',').ToList();
                        string RedisPass = con.GetSection("Redis").GetSection("RedisPassword").GetSection(Enviornment).Value.ToString();
                        ConfigurationOptions option = new()
                        {
                            AbortOnConnectFail = false,
                            ConnectTimeout = 1000,
                            SyncTimeout = 2000,
                            ConnectRetry = 1,
                            Password = RedisPass,
                            Ssl = true
                        };

                        foreach (var node in nodes)
                        {
                            option.EndPoints.Add(node);
                        }

                        connection = ConnectionMultiplexer.Connect(option);
                        _redisList.Add(connection);
                    }
                }
            }

            return connection.GetDatabase();
        }

        public static string GetRedisData(string Key)
        {
            IDatabase db = GetDatabase();
            string data = db.StringGet(Key, CommandFlags.PreferReplica);
            return data;
        }

        public static void SetRedisData(string Key, string Value, TimeSpan timeSpan)
        {
            IDatabase db = GetDatabase();
            db.StringSet(Key, Value);
            db.KeyExpire(Key, timeSpan);
        }

        public static void SetRedisData(string Key, string Value)
        {
            IDatabase db = GetDatabase();
            db.StringSet(Key, Value);
        }

        public static void MSetRedisData(Dictionary<string, string> KeyValueList)
        {
            IDatabase db = GetDatabase();
            if (KeyValueList.Count != 0)
            {
                int size = KeyValueList.Count;
                var set = from item in KeyValueList
                          select new KeyValuePair<RedisKey, RedisValue>(item.Key, item.Value);
                if (set.Any())
                    db.StringSet(set.ToArray());
            }
        }

        public static void SetZAdd(string Key, string Value, TimeSpan timeSpan)
        {
            IDatabase db = GetDatabase();
            long sort = Convert.ToInt64(DateTime.Now.ToString("yyyyMMddHHmmss"));
            db.SortedSetAdd(Key, Value, sort);
            db.KeyExpire(Key, timeSpan);
        }
    }
}