FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build-env
WORKDIR /app

# Copy everything and restore as distinct layers
COPY . ./
WORKDIR /app/Service
RUN dotnet restore

# Build and publish
RUN dotnet publish -c Release -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:9.0
RUN cp /usr/share/zoneinfo/Asia/Kolkata /etc/localtime
RUN rm -rf /etc/localtime
RUN ln -s /usr/share/zoneinfo/Asia/Kolkata /etc/localtime
WORKDIR /app
ENV ASPNETCORE_HTTP_PORTS=80
EXPOSE 80
COPY --from=build-env /app/Service/out .
ENTRYPOINT ["dotnet", "Service.dll"] 