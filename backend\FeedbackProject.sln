Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Service", "Service\Service.csproj", "{E5F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BusinessLogicLayer", "BusinessLogicLayer\BusinessLogicLayer.csproj", "{F6F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataAccessLayer", "DataAccessLayer\DataAccessLayer.csproj", "{G7F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataHelper", "DataHelper\DataHelper.csproj", "{H8F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PropertyLayers", "PropertyLayers\PropertyLayers.csproj", "{I9F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MongoConfigProject", "MongoConfigProject\MongoConfigProject.csproj", "{J0F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kafka", "kafka\Kafka.csproj", "{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingHelper", "LoggingHelper\LoggingHelper.csproj", "{K1F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}"
	ProjectSection(ProjectDependencies) = postProject
		{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B} = {L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E5F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E5F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E5F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E5F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{G7F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{G7F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{G7F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{G7F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{H8F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{H8F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{H8F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{H8F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{I9F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{I9F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{I9F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{I9F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{J0F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{J0F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{J0F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{J0F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{K1F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{K1F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{K1F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{K1F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
		{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{L2F9E4E4-8F1E-4E4B-8F1E-4E4B8F1E4E4B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {J0K1L2M3-N4O5-3210-8765-9ABCDEF01243}
	EndGlobalSection
EndGlobal 