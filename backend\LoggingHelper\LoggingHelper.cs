using MongoDB.Bson;
using Newtonsoft.Json;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;
using Helper;
using PropertyLayers;
using Kafka;

namespace LoggingHelper
{
    public static class LoggingHelper
    {
        static LoggingHelper()
        {
        }

        public static void Log(string strUniqueID, long LeadID, string strexCeption, string strMethodName, string ApplicationName, string CreatedBy, string strRequestText, string strResponseText, DateTime RequestDateTime, DateTime ResponseDateTime)
        {
            try
            {
                string json = JsonConvert.SerializeObject(getLoggingModel(strUniqueID, LeadID, strexCeption, strMethodName, ApplicationName, CreatedBy, "", "", DateTime.Now, DateTime.Now));
                Console.WriteLine("WriteFileLog-" + json);
            }
            catch (Exception ex)
            {
                Console.WriteLine("WriteFileLog-error" + ex.ToString());
            }
        }

        public static void AddloginQueue(string strUniqueID, long LeadID, string strexCeption, string strMethodName, string ApplicationName, string CreatedBy, string strRequestText, string strResponseText, DateTime RequestDateTime, DateTime ResponseDateTime)
        {
            try
            {
                LoggingModel loggingModel = getLoggingModel(strUniqueID, LeadID, strexCeption, strMethodName, ApplicationName, CreatedBy, strRequestText, strResponseText, RequestDateTime, ResponseDateTime);

                if (CoreCommonMethods.IsValidString(loggingModel.Exception))
                {
                    Console.WriteLine("Error => " + JsonConvert.SerializeObject(loggingModel));
                }

                LoggingRequest loggingRequest = new()
                {
                    ConnectionName = "MATRIX_MONGODB_LOGGER",
                    DbName = "logger",
                    CollectionName = "Log_Collection",
                    RequestPayload = JsonConvert.SerializeObject(loggingModel)
                };

                KafkaWrapper.PushToKafka(loggingRequest);
            }
            catch (Exception ex)
            {
                Log(strUniqueID, LeadID, ex.ToString(), strMethodName, ApplicationName, CreatedBy, strRequestText, strResponseText, RequestDateTime, ResponseDateTime);
            }
        }

        private static LoggingModel getLoggingModel(string strUniqueID, long LeadID, string strexCeption, string strMethodName, string ApplicationName, string CreatedBy, string strRequestText, string strResponseText, DateTime RequestDateTime, DateTime ResponseDateTime)
        {
            LoggingModel objLoggingModel = new();
            if (!string.IsNullOrEmpty(strUniqueID))
                objLoggingModel.TrackingID = strUniqueID;
            else
                objLoggingModel.TrackingID = LeadID.ToString();
            objLoggingModel.Exception = strexCeption;
            objLoggingModel.Method = strMethodName;
            objLoggingModel.Application = ApplicationName;
            objLoggingModel.RequestText = strRequestText;
            objLoggingModel.ResponseText = strResponseText;
            objLoggingModel.Requesttime = RequestDateTime;
            objLoggingModel.Responsetime = ResponseDateTime;
            objLoggingModel.CreatedOn = DateTime.Now;
            objLoggingModel.CreatedBy = CreatedBy;

            return objLoggingModel;
        }
    }

    [DataContract]
    public class LoggingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public ObjectId Id { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? IP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? Channel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? Application { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? Method { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? TrackingID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? RequestText { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? ResponseText { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime Requesttime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime Responsetime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? Exception { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string? CreatedBy { get; set; }
    }
} 