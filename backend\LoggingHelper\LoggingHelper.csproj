<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MongoDB.Driver" Version="2.24.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\DataHelper\DataHelper.csproj" />
		<ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
		<ProjectReference Include="..\kafka\Kafka.csproj" />
	</ItemGroup>

</Project> 