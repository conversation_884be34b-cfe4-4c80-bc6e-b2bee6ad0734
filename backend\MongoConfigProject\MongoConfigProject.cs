using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using PropertyLayers;
using System.Runtime.Caching;
using System;
using System.Collections.Generic;
using System.Linq;
using Custom;
using Helper;

namespace MongoConfigProject
{
    public static class MongoConfigProject
    {
        private const int CacheExpirationMinutes = 8 * 60;

        public static string AppSettings(this string appKey)
        {
            string KeyName = string.Empty;

            try
            {
                IConfiguration config = Custom.ConfigurationManager.AppSetting;
                string collection = config.GetSection("ConfigKey").Value.ToString();

                if (string.IsNullOrEmpty(collection))
                    return string.Empty;

                List<ConfigData> configValue = GetConfigFromMongo(collection);
                if (configValue.Count > 0)
                    KeyName = configValue.Where(x => x.key.Contains(appKey) && x.key == appKey).Select(x => x.value).FirstOrDefault();
            }
            catch (Exception)
            {
                return string.Empty;
            }
            return KeyName;
        }

        private static List<ConfigData> GetConfigFromMongo(string Collection)
        {
            try
            {
                ObjectCache cache = MemoryCache.Default;
                List<ConfigData> sysConfigs;
                if (cache.Get(Collection) != null)
                    sysConfigs = (List<ConfigData>)cache.Get(Collection);
                else
                {
                    sysConfigs = FetchConfigsFromMongo(Collection);
                    if (sysConfigs.Count > 0)
                    {
                        cache.Add(Collection, sysConfigs, new CacheItemPolicy
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(CacheExpirationMinutes)
                        });
                    }
                }

                return sysConfigs;
            }
            catch (Exception ex)
            {
                return [];
            }
        }

        private static List<ConfigData> FetchConfigsFromMongo(string collection)
        {
            var config = Custom.ConfigurationManager.AppSetting;
            string Enviornment = CoreCommonMethods.GetEnvironmentVar();
            var connectionString = config.GetSection("MongoDB").GetSection("OneLeadDBConnection").GetSection(Enviornment).Value.ToString();
            var database = config.GetSection("MongoDB").GetSection("OneLeadDB").Value.ToString();

            if (string.IsNullOrEmpty(connectionString) || string.IsNullOrEmpty(database))
                return [];

            var client = new MongoClient(connectionString);
            var db = client.GetDatabase(database);
            var configCollection = db.GetCollection<ConfigData>(collection);

            var projection = Builders<ConfigData>.Projection
                .Include(x => x.key)
                .Include(x => x.value)
                .Include("_id");

            return configCollection.Find(Builders<ConfigData>.Filter.Empty)
                .Project<ConfigData>(projection)
                .ToList();
        }
    } 
}

