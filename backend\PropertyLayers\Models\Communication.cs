using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
  
    public class sendcommunicationResponse
    {
        public string TriggerName { get; set; }
        public long LeadId { get; set; }
        public int CommunicationType { get; set; }
        public long MobileNo { get; set; }
        public int ProductId { get; set; }
        public Inputdata InputData { get; set; }
        public string[] To { get; set; }
        public bool IsSalesWA { get; set; }
        public string ProductDisplayName { get; set; }
        public string AgentEcode { get; set; }
        public string AgentName { get; set; }
        public string FeedbackType { get; set; }
        public Inputdata CsatData { get; set; }
        public int CountryCode { get; set; }
        public string CreatedBy { get; set; }
        public string[] Bcc { get; set; }
    }

    public class Inputdata
    {
        public string CustomerName { get; set; }
        public string ContinueLink { get; set; }
        public string RenewalLink { get; set; }
        public string OTP { get; set; }
        public string LeadID { get; set; }
        public string Source { get; set; }
        public string ToMobileNo { get; set; }
        public string ProductName { get; set; }
        public string AgentName { get; set; }
        public string AgentEcode { get; set; }
        public string CallTypeId { get; set; }
        public string CallUId { get; set; }

        public string ScheduleDate { get; set; }
        public string ScheduleDay { get; set; }
        public string ScheduleTime { get; set; }
        public string ProductDisplayName { get; set; }
        public string NAME { get; set; }

        public string ScheduleCallbackLink { get; set; }
        public string INSURER { get; set; }
        public string LINK5 { get; set; }
        public string LINK { get; set; }
        public string PRODUCT { get; set; }
        public string InsurerName { get; set; }
        public string PolicyNo { get; set; }
        public string date { get; set; }
        public string amount { get; set; }
        public string VideoLink { get; set; }


        public string ShortKYAUrl { get; set; }
        public string AdvisorName { get; set; }

        public string CustomerAddress { get; set; }
        public string AppointmentDateTime { get; set; }
        public string AppointmentID { get; set; }
        public string AppointmentUID { get; set; }
        public string AppointmentSubStatusID { get; set; }
        public string AgentEmpID { get; set; }
        public string FOSLink { get; set; }
        public long FinalPremium { get; set; }

        public string Subject { get; set; }

        public string Content { get; set; }

    }


    public class User
    {
        public string UserId { get; set; }
        public string AsteriskToken { get; set; }
        public string EmployeeId { get; set; }
        public int GroupId { get; set; }
    }

}
