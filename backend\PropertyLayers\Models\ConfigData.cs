using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class SysConfigData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        
        public object ID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string clientKey { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string authKey { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string createdBy { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string EncKey { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string EncIV { get; set; }
    }


    [DataContract]
    [Serializable]
    public class ACLConfigData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]

        public object ID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string method { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool isActive { get; set; }
    }

    [DataContract]
    [Serializable]
    public class ConfigData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]

        public object ID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string value { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string key { get; set; }

    }

}
