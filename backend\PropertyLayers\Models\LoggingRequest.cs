using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    public class LoggingRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public string CollectionName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DbName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ConnectionName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RequestPayload { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Method { get; set; }
    }
} 