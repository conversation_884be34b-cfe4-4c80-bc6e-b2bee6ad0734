using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class SalesTicketModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string? TicketID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? TicketDisplayID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? Title { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SourceID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int IssueID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CreatedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? Comments { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? Process { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? IssueStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? TicketStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<TicketComments>? Commentlist { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? FileName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? FileURL { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ReplyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int StatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Empdata? CreatedByDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Empdata? AssignToDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ProcessID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? ProductName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? MatrixRole { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? BU { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte IsSatisfied { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Empdata? ManagerDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? RefId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? ParentID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? PayID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? OrderID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? RefTicketID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsStatusChanged { get; set; }
    }

    public class Result
    {
        [DataMember]
        public string Description { get; set; }
        [DataMember]
        public string MessageCode { get; set; }
        [DataMember]
        public int Status { get; set; }
        [DataMember]
        public string TicketDisplayId { get; set; }
    }

    public class SalesTicketStatus
    {
        public int StatusID { get; set; }
        public string StatusName { get; set; }
        public byte Sequence { get; set; }
        public int Count { get; set; }

        public int StatusCode { get; set; }
    }

    public class TicketComments
    {
        public int TicketID { get; set; }
        public string Comment { get; set; }
        public string FileURL { get; set; }

        public string FileName { get; set; }

        public byte ReplyType { get; set; }

        public SalesTicketUser User { get; set; }

        [DataMember]
        public DateTime CreatedOn { get; set; }

        [DataMember]
        public string RefId { get; set; }

    }

    public class SalesTicketUser
    {
        public int UserID { get; set; }

        public string EmployeeId { get; set; }

        public string UserName { get; set; }
    }

    public class SearchTicket
    {
        public int EmpID { get; set; }
        public string FromDate { get; set; }

        public string ToDate { get; set; }

        public int ProcessID { get; set; }

        public int IssueID { get; set; }

        public int StatusID { get; set; }

        public int? TicketID { get; set; }

        public string TicketDisplayID { get; set; }

        public int AssignTo { get; set; }

        public int ProductID { get; set; }
        public int FeedBackTypeID { get; set; }

    }

    public class SalesTicketAssignment
    {
        public int TicketID { get; set; }
        public int ProcessId { get; set; }
        public int AsssignToUserID { get; set; }
        public int AsssignByUserID { get; set; }
    }

    public class Process
    {
        public int SourceID { get; set; }
        public string Name { get; set; }
    }

    public class Issue
    {
        public int IssueID { get; set; }

        public string ISSUENAME { get; set; }

        public int SubIssueID { get; set; }

        public int SUBISSUENAME { get; set; }

        public int SourceID { get; set; }

    }

    public class SalesTicketlog
    {
        public int TicketId { get; set; }

        public string FieldName { get; set; }

        public string OldValue { get; set; }

        public string NewValue { get; set; }

        public DateTime CreatedOn { get; set; }

        public string CreatedByName { get; set; }

    }

    public class Notification
    {
        public string id { get; set; }
        public string type { get; set; }
        public string _event { get; set; }
        public string text { get; set; }
        public string empID { get; set; }
        public object productID { get; set; }
        public string link { get; set; }
    }


    [DataContract]
    public class MailAttachments
    {
        [DataMember]
        public string? FileID { get; set; }

        [DataMember]
        public string AttachmentURL { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FileName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int FileSize { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AttachemntContent { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ContentType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string? RefId { get; set; }
    }

    public class MiscDocUploadRequest
    {
        public long LeadId { get; set; }

        public long CustomerId { get; set; }

        public int ProductId { get; set; }

        public string EnquiryId { get; set; }

        public string Type { get; set; }

        public string RefId { get; set; }
    }

    public class MiscDocUploadResponse
    {
        public string DocUrl { get; set; }

        public string DocId { get; set; }

        public string TtlDocUrl { get; set; }

        public long ExpiresInSec { get; set; }

        public string Message { get; set; }

        public bool StatusSuccessful { get; set; }

        public string Signature { get; set; }
    }


    public class DocumentUrlResponse
    {
        public string fileName { get; set; }

        public int expiresInSec { get; set; }

        public string mimeType { get; set; }

        public string ttlDocUrl { get; set; }

    }

    public class DocumentUrlRequest
    {
        public string CustomerId { get; set; }

        public string DocumentId { get; set; }

        public int DocumentTypeId { get; set; }

        public string RefId { get; set; }

    }

    public class TicketGroup
    {
        public int Count { get; set; }
        public List<Ticket> Tickets { get; set; }
    }

    public class Ticket
    {
        public string TicketDisplayID { get; set; }
        public string TicketID { get; set; }
        public string Process { get; set; }
        public DateTime CreatedOn { get; set; }
        public string? IssueStatus { get; set; }
        public string TicketStatus { get; set; }
        public DateTime UpdatedOn { get; set; }
        public string AssignToEmployeeID { get; set; }
        public string AssignToUserName { get; set; }
        public string CreatedByEmployeeId { get; set; }
        public string CreatedByUserName { get; set; }
        public int ProductId { get; set; }
    }

    public class SalesAgent
    {
        public Empdata[] EMPData { get; set; }
        public string Token { get; set; }
        public int IsLocSet { get; set; }
        public Location[] Location { get; set; }
        public Issue Issue { get; set; }
        public Locationmaster[] LocationMaster { get; set; }


    }

    public class Empdata
    {
        public int EmpID { get; set; }
        public string EmployeeID { get; set; }
        public string EmailID { get; set; }
        public string Name { get; set; }
        public string RoleName { get; set; }
        public int RoleID { get; set; }
        public string CompanyName { get; set; }
        public string SpocDepartment { get; set; }
        public string FunctionName { get; set; }
        public string DepartmentName { get; set; }

        public int MatrixUserID { get; set; }

        public int ProcessID { get; set; }
        public string UserDisplayName { get; set; }

        public int BU { get; set; }

        public long ContactNo { get; set; }
        public long ManagerNo { get; set; }

        public long MatrixRoleId { get; set; }

        public string AssignToEmailId { get; set; }

        public byte Userlevel { get; set; }
    }

    public class Location
    {
        public object State { get; set; }
        public string City { get; set; }
        public string Sector { get; set; }
        public string Building { get; set; }
        public string FloorNo { get; set; }
        public string Seat { get; set; }
    }

    public class Locationmaster
    {
        public int state_id { get; set; }
        public string state_name { get; set; }
        public int city_id { get; set; }
        public string city_name { get; set; }
        public int area_id { get; set; }
        public string area_name { get; set; }
        public int building_id { get; set; }
        public string building_name { get; set; }
        public string floor_name { get; set; }
        public int floor_id { get; set; }
    }


} 