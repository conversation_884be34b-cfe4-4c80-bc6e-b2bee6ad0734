{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"PropertyLayers/1.0.0": {"dependencies": {"MongoDB.Driver": "2.12.3"}, "runtime": {"PropertyLayers.dll": {}}}, "DnsClient/1.4.0": {"runtime": {"lib/netstandard2.1/DnsClient.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.4.0.0"}}}, "Microsoft.NETCore.Platforms/2.1.2": {}, "MongoDB.Bson/2.12.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "2.12.3.0", "fileVersion": "2.12.3.0"}}}, "MongoDB.Driver/2.12.3": {"dependencies": {"MongoDB.Bson": "2.12.3", "MongoDB.Driver.Core": "2.12.3", "MongoDB.Libmongocrypt": "1.2.1"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "2.12.3.0", "fileVersion": "2.12.3.0"}}}, "MongoDB.Driver.Core/2.12.3": {"dependencies": {"DnsClient": "1.4.0", "MongoDB.Bson": "2.12.3", "MongoDB.Libmongocrypt": "1.2.1", "SharpCompress": "0.23.0", "System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "2.12.3.0", "fileVersion": "2.12.3.0"}}, "runtimeTargets": {"runtimes/win/native/libzstd.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/snappy32.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}, "runtimes/win/native/snappy64.dll": {"rid": "win", "assetType": "native", "fileVersion": "1.1.1.7"}}}, "MongoDB.Libmongocrypt/1.2.1": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.2.1.0", "fileVersion": "1.2.1.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SharpCompress/0.23.0": {"dependencies": {"System.Text.Encoding.CodePages": "4.5.1"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "0.23.0.0", "fileVersion": "0.23.0.0"}}}, "System.Buffers/4.5.1": {}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}}}}, "libraries": {"PropertyLayers/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "DnsClient/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-CO1NG1zQdV0nEAXmr/KppLZ0S1qkaPwV0kPX5YPgmYBtrBVh1XMYHM54IXy3RBJu1k4thFtpzwo4HNHqxiuFYw==", "path": "dnsclient/1.4.0", "hashPath": "dnsclient.1.4.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "MongoDB.Bson/2.12.3": {"type": "package", "serviceable": true, "sha512": "sha512-1gkSkX+hlyeNMvrcx4JeeGnTqAmOctoekczmASEzxuQiA15wo2YKm40TVx/pFbZGV57ekguzF8w2nurO5+2IQg==", "path": "mongodb.bson/2.12.3", "hashPath": "mongodb.bson.2.12.3.nupkg.sha512"}, "MongoDB.Driver/2.12.3": {"type": "package", "serviceable": true, "sha512": "sha512-tRjJixeBeA7Gus49kdf3t9uKUwzRTpN8S2OGkrusZw+nwSeFOH0K5VEjBpVgQEekwJLqnUNd1IkhKd5nK2Fz7A==", "path": "mongodb.driver/2.12.3", "hashPath": "mongodb.driver.2.12.3.nupkg.sha512"}, "MongoDB.Driver.Core/2.12.3": {"type": "package", "serviceable": true, "sha512": "sha512-wb5F6J5eolHwSLtIlCpENEYY05CvCtWt79ptVdmVTLbFq58dbsu6rEfxEtd9V7lkyPCeUoJuiSkCxceXx9mMgQ==", "path": "mongodb.driver.core/2.12.3", "hashPath": "mongodb.driver.core.2.12.3.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-VUtUAO2W6+DOjBtLHuD9WlTTcxYLXCNfp9YeKcws5LNAl0xej2KX1I37VsTAKnmnMjAF2Vwy1FXLCpK84FglMA==", "path": "mongodb.libmongocrypt/1.2.1", "hashPath": "mongodb.libmongocrypt.1.2.1.nupkg.sha512"}, "SharpCompress/0.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-HBbT47JHvNrsZX2dTBzUBOSzBt+EmIRGLIBkbxcP6Jef7DB4eFWQX5iHWV3Nj7hABFPCjISrZ8s0z72nF2zFHQ==", "path": "sharpcompress/0.23.0", "hashPath": "sharpcompress.0.23.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}}}