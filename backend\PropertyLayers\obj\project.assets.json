{"version": 3, "targets": {"net9.0": {"DnsClient/1.4.0": {"type": "package", "compile": {"lib/netstandard2.1/DnsClient.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/DnsClient.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "MongoDB.Bson/2.12.3": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"related": ".xml"}}}, "MongoDB.Driver/2.12.3": {"type": "package", "dependencies": {"MongoDB.Bson": "2.12.3", "MongoDB.Driver.Core": "2.12.3", "MongoDB.Libmongocrypt": "1.2.1"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"related": ".xml"}}}, "MongoDB.Driver.Core/2.12.3": {"type": "package", "dependencies": {"DnsClient": "1.4.0", "MongoDB.Bson": "2.12.3", "MongoDB.Libmongocrypt": "1.2.1", "SharpCompress": "0.23.0", "System.Buffers": "4.5.1"}, "compile": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"related": ".xml"}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/win/native/libzstd.dll": {"assetType": "native", "rid": "win"}, "runtimes/win/native/snappy32.dll": {"assetType": "native", "rid": "win"}, "runtimes/win/native/snappy64.dll": {"assetType": "native", "rid": "win"}}}, "MongoDB.Libmongocrypt/1.2.1": {"type": "package", "compile": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}, "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"assetType": "native", "rid": "linux"}, "runtimes/osx/native/libmongocrypt.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win/native/mongocrypt.dll": {"assetType": "native", "rid": "win"}}}, "SharpCompress/0.23.0": {"type": "package", "dependencies": {"System.Text.Encoding.CodePages": "4.5.1"}, "compile": {"lib/netstandard2.0/SharpCompress.dll": {}}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"DnsClient/1.4.0": {"sha512": "CO1NG1zQdV0nEAXmr/KppLZ0S1qkaPwV0kPX5YPgmYBtrBVh1XMYHM54IXy3RBJu1k4thFtpzwo4HNHqxiuFYw==", "type": "package", "path": "dnsclient/1.4.0", "files": [".nupkg.metadata", ".signature.p7s", "dnsclient.1.4.0.nupkg.sha512", "dnsclient.nuspec", "icon.png", "lib/net45/DnsClient.dll", "lib/net45/DnsClient.xml", "lib/net471/DnsClient.dll", "lib/net471/DnsClient.xml", "lib/netstandard1.3/DnsClient.dll", "lib/netstandard1.3/DnsClient.xml", "lib/netstandard2.0/DnsClient.dll", "lib/netstandard2.0/DnsClient.xml", "lib/netstandard2.1/DnsClient.dll", "lib/netstandard2.1/DnsClient.xml"]}, "Microsoft.NETCore.Platforms/2.1.2": {"sha512": "mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "type": "package", "path": "microsoft.netcore.platforms/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.1.2.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "MongoDB.Bson/2.12.3": {"sha512": "1gkSkX+hlyeNMvrcx4JeeGnTqAmOctoekczmASEzxuQiA15wo2YKm40TVx/pFbZGV57ekguzF8w2nurO5+2IQg==", "type": "package", "path": "mongodb.bson/2.12.3", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "lib/net452/MongoDB.Bson.dll", "lib/net452/MongoDB.Bson.xml", "lib/netstandard1.5/MongoDB.Bson.dll", "lib/netstandard1.5/MongoDB.Bson.xml", "lib/netstandard2.0/MongoDB.Bson.dll", "lib/netstandard2.0/MongoDB.Bson.xml", "lib/netstandard2.1/MongoDB.Bson.dll", "lib/netstandard2.1/MongoDB.Bson.xml", "mongodb.bson.2.12.3.nupkg.sha512", "mongodb.bson.nuspec", "packageIcon.png"]}, "MongoDB.Driver/2.12.3": {"sha512": "tRjJixeBeA7Gus49kdf3t9uKUwzRTpN8S2OGkrusZw+nwSeFOH0K5VEjBpVgQEekwJLqnUNd1IkhKd5nK2Fz7A==", "type": "package", "path": "mongodb.driver/2.12.3", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "lib/net452/MongoDB.Driver.dll", "lib/net452/MongoDB.Driver.xml", "lib/netstandard1.5/MongoDB.Driver.dll", "lib/netstandard1.5/MongoDB.Driver.xml", "lib/netstandard2.0/MongoDB.Driver.dll", "lib/netstandard2.0/MongoDB.Driver.xml", "lib/netstandard2.1/MongoDB.Driver.dll", "lib/netstandard2.1/MongoDB.Driver.xml", "mongodb.driver.2.12.3.nupkg.sha512", "mongodb.driver.nuspec", "packageIcon.png"]}, "MongoDB.Driver.Core/2.12.3": {"sha512": "wb5F6J5eolHwSLtIlCpENEYY05CvCtWt79ptVdmVTLbFq58dbsu6rEfxEtd9V7lkyPCeUoJuiSkCxceXx9mMgQ==", "type": "package", "path": "mongodb.driver.core/2.12.3", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "THIRD-PARTY-NOTICES", "build/MongoDB.Driver.Core.targets", "content/Core/Compression/Snappy/lib/win/snappy32.dll", "content/Core/Compression/Snappy/lib/win/snappy64.dll", "content/Core/Compression/Zstandard/lib/win/libzstd.dll", "contentFiles/any/net452/Core/Compression/Snappy/lib/win/snappy32.dll", "contentFiles/any/net452/Core/Compression/Snappy/lib/win/snappy64.dll", "contentFiles/any/net452/Core/Compression/Zstandard/lib/win/libzstd.dll", "contentFiles/any/netstandard1.5/Core/Compression/Snappy/lib/win/snappy32.dll", "contentFiles/any/netstandard1.5/Core/Compression/Snappy/lib/win/snappy64.dll", "contentFiles/any/netstandard1.5/Core/Compression/Zstandard/lib/win/libzstd.dll", "contentFiles/any/netstandard2.0/Core/Compression/Snappy/lib/win/snappy32.dll", "contentFiles/any/netstandard2.0/Core/Compression/Snappy/lib/win/snappy64.dll", "contentFiles/any/netstandard2.0/Core/Compression/Zstandard/lib/win/libzstd.dll", "contentFiles/any/netstandard2.1/Core/Compression/Snappy/lib/win/snappy32.dll", "contentFiles/any/netstandard2.1/Core/Compression/Snappy/lib/win/snappy64.dll", "contentFiles/any/netstandard2.1/Core/Compression/Zstandard/lib/win/libzstd.dll", "lib/net452/MongoDB.Driver.Core.dll", "lib/net452/MongoDB.Driver.Core.xml", "lib/netstandard1.5/MongoDB.Driver.Core.dll", "lib/netstandard1.5/MongoDB.Driver.Core.xml", "lib/netstandard2.0/MongoDB.Driver.Core.dll", "lib/netstandard2.0/MongoDB.Driver.Core.xml", "lib/netstandard2.1/MongoDB.Driver.Core.dll", "lib/netstandard2.1/MongoDB.Driver.Core.xml", "mongodb.driver.core.2.12.3.nupkg.sha512", "mongodb.driver.core.nuspec", "packageIcon.png", "runtimes/win/native/libzstd.dll", "runtimes/win/native/snappy32.dll", "runtimes/win/native/snappy64.dll"]}, "MongoDB.Libmongocrypt/1.2.1": {"sha512": "VUtUAO2W6+DOjBtLHuD9WlTTcxYLXCNfp9YeKcws5LNAl0xej2KX1I37VsTAKnmnMjAF2Vwy1FXLCpK84FglMA==", "type": "package", "path": "mongodb.libmongocrypt/1.2.1", "files": [".nupkg.metadata", ".signature.p7s", "License.txt", "build/MongoDB.Libmongocrypt.targets", "content/libmongocrypt.dylib", "content/libmongocrypt.so", "content/mongocrypt.dll", "contentFiles/any/net452/libmongocrypt.dylib", "contentFiles/any/net452/libmongocrypt.so", "contentFiles/any/net452/mongocrypt.dll", "contentFiles/any/netstandard1.5/libmongocrypt.dylib", "contentFiles/any/netstandard1.5/libmongocrypt.so", "contentFiles/any/netstandard1.5/mongocrypt.dll", "contentFiles/any/netstandard2.0/libmongocrypt.dylib", "contentFiles/any/netstandard2.0/libmongocrypt.so", "contentFiles/any/netstandard2.0/mongocrypt.dll", "contentFiles/any/netstandard2.1/libmongocrypt.dylib", "contentFiles/any/netstandard2.1/libmongocrypt.so", "contentFiles/any/netstandard2.1/mongocrypt.dll", "lib/net452/MongoDB.Libmongocrypt.dll", "lib/netstandard1.5/MongoDB.Libmongocrypt.dll", "lib/netstandard2.0/MongoDB.Libmongocrypt.dll", "lib/netstandard2.1/MongoDB.Libmongocrypt.dll", "mongodb.libmongocrypt.1.2.1.nupkg.sha512", "mongodb.libmongocrypt.nuspec", "runtimes/linux/native/libmongocrypt.so", "runtimes/osx/native/libmongocrypt.dylib", "runtimes/win/native/mongocrypt.dll"]}, "SharpCompress/0.23.0": {"sha512": "HBbT47JHvNrsZX2dTBzUBOSzBt+EmIRGLIBkbxcP6Jef7DB4eFWQX5iHWV3Nj7hABFPCjISrZ8s0z72nF2zFHQ==", "type": "package", "path": "sharpcompress/0.23.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/SharpCompress.dll", "lib/net45/SharpCompress.dll", "lib/netstandard1.0/SharpCompress.dll", "lib/netstandard1.3/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "sharpcompress.0.23.0.nupkg.sha512", "sharpcompress.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding.CodePages/4.5.1": {"sha512": "4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "type": "package", "path": "system.text.encoding.codepages/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.5.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["MongoDB.Driver >= 2.12.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "projectName": "PropertyLayers", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"MongoDB.Driver": {"target": "Package", "version": "[2.12.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'MongoDB.Driver' 2.12.3 has a known high severity vulnerability, https://github.com/advisories/GHSA-7j9m-j397-g4wx", "libraryId": "MongoDB.Driver", "targetGraphs": ["net9.0"]}]}