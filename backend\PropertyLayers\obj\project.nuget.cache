{"version": 2, "dgSpecHash": "0IDYo7fcYcQ=", "success": true, "projectFilePath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.4.0\\dnsclient.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.1.2\\microsoft.netcore.platforms.2.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\2.12.3\\mongodb.bson.2.12.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\2.12.3\\mongodb.driver.2.12.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver.core\\2.12.3\\mongodb.driver.core.2.12.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.libmongocrypt\\1.2.1\\mongodb.libmongocrypt.1.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.23.0\\sharpcompress.0.23.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.1\\system.text.encoding.codepages.4.5.1.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "message": "Package 'MongoDB.Driver' 2.12.3 has a known high severity vulnerability, https://github.com/advisories/GHSA-7j9m-j397-g4wx", "projectPath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "warningLevel": 1, "filePath": "D:\\pb\\New folder\\matrixfeedback\\backend\\PropertyLayers\\PropertyLayers.csproj", "libraryId": "MongoDB.Driver", "targetGraphs": ["net9.0"]}]}