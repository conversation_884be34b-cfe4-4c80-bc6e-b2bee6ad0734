using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace Service.Controllers
{
    [Route("[controller]/[action]")]
    public class ErrorMessageController : ControllerBase
    {
        private readonly ILogger<ErrorMessageController> _logger;
        
        public ErrorMessageController(ILogger<ErrorMessageController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IActionResult NonAuthorised()
        {
            Debug.WriteLine("Invalid Token debug.");
            _logger.LogError("Invalid Token");
            return new ContentResult { StatusCode = 401, Content = "Invalid Token", ContentType = "text/plain" };
        }

        [HttpGet]
        public IActionResult NonAccess()
        {
            return new ContentResult { StatusCode = 401, Content = "You don't have permission", ContentType = "text/plain" };
        }
    }
} 