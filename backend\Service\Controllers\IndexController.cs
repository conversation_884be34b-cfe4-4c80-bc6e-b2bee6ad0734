﻿using System;
using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace Service
{
    [ApiController]
    [Route("feedback/api/[controller]/[action]")]

    public class IndexController : ControllerBase
    {
        [HttpGet]
        public ResponseAPI HealthCheck()
        {
            ResponseAPI Response = new();
            Response.status = true;
            Response.message = "Success";

            return Response;
        }
    }
}

