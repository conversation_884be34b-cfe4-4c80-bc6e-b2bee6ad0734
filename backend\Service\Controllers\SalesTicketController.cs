using BusinessLogicLayer;
using PropertyLayers;
using Microsoft.AspNetCore.Mvc;
using Service.Helper;

namespace Service
{
    //[CommAuth]
    [ApiController]
    [Route("feedback/api/[controller]/[action]")]
    public class SalesTicketController : ControllerBase
    {
        private readonly ISalesTicketBAL salesTicketBAL;

        public SalesTicketController(ISalesTicketBAL _salesTicketBAL)
        {
            salesTicketBAL = _salesTicketBAL;
        }

        [HttpPost]
        public Result CreateTicket([FromBody] SalesTicketModel ticket)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            ticket.CreatedBy = Convert.ToInt32(AgentId);
            return salesTicketBAL.CreateTicket(ticket);
        }

        [HttpGet("{type}")]
        public List<SalesTicketStatus> GetSalesTicketCount(int type, int? status = null)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            int userId = Convert.ToInt32(AgentId);
            return salesTicketBAL.GetSalesTicketCount(userId, status ?? 0, type);
        }

        [HttpGet("{ticketId}")]
        public SalesTicketModel GetSalesTicketDetailsByID(int ticketId)
        {
            return salesTicketBAL.GetSalesTicketDetailsByID(ticketId);
        }

        [HttpPost]
        public Result UpdateSalesTicketByID([FromBody] SalesTicketModel ticket)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            ticket.CreatedBy = Convert.ToInt32(AgentId);
            return salesTicketBAL.UpdateSalesTicketByID(ticket);
        }

        [HttpPost]
        public List<SalesTicketModel> SalesTicketDashboardSearch([FromBody] SearchTicket searchCriteria)
        {
            return salesTicketBAL.SalesTicketDashboardSearch(searchCriteria);
        }

        [HttpGet]
        public List<SalesTicketStatus> GetSalesTicketStatusMaster()
        {
            return salesTicketBAL.GetSalesTicketStatusMaster();
        }

        [HttpPost]
        public Result AssignSalesTicket([FromBody] SalesTicketAssignment assignment)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            assignment.AsssignByUserID = Convert.ToInt32(AgentId);
            return salesTicketBAL.AssignSalesTicket(assignment);
        }

        [HttpGet]
        public List<Process> GetProcessMaster()
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            return salesTicketBAL.GetSalesProcessMaster(Convert.ToInt64(AgentId));
        }

        [HttpGet]

        public List<Issue> GetAllIssueSubIssue()
        {
            return salesTicketBAL.GetSalesIssueMaster();
        }

        [HttpGet("{type}")]
        public List<SalesTicketModel> GetSalesTicketByAgentId(int type, int? status = null)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            return salesTicketBAL.GetSalesTicketByAgentId(Convert.ToInt32(AgentId), status ?? 0, type);
        }

        [HttpGet("{ticketId}/{ProcessId}")]
        public List<Empdata> GetSalesTicketProcessUser(int ticketId, int ProcessId)
        {
            return salesTicketBAL.GetSalesTicketProcessUser(ticketId, ProcessId);
        }


        [HttpGet("{ticketId}/{logtype}")]
        public List<SalesTicketlog> GetSalesTicketLog(int ticketId, int logtype)
        {
            string userId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            return salesTicketBAL.GetSalesTicketLog(ticketId, logtype, Convert.ToInt32(userId));
        }

        [HttpPost]
        public List<MailAttachments> UploadFile(List<MailAttachments> objMailAttachments)
        {
            return salesTicketBAL.UploadFile(objMailAttachments);
        }

        [HttpGet("{type}")]
        public Dictionary<string, TicketGroup> GetSpanCreatedTickets(int type)
        {
            string UserId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            return salesTicketBAL.GetSpanCreatedTickets(Convert.ToInt32(UserId), type);
        }

        [HttpPost]
        public Result ReAssignSalesTicket([FromBody] SalesTicketAssignment obj)
        {
            string AgentId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
            obj.AsssignByUserID = Convert.ToInt32(AgentId);
            return salesTicketBAL.ReAssignSalesTicket(obj);
        }


        [HttpGet]
        public SalesAgent GetSalesTicketUserDetails()
        {
            {
                string userId = Request != null ? Request.Headers["AgentId"].ToString() : string.Empty;
                return salesTicketBAL.GetSalesTicketUserDetails(Convert.ToInt64(userId));
            }
        }

        [HttpPost]
        public Result UpdateUserProfile([FromBody] Empdata obj)
        {
            return salesTicketBAL.UpdateSalesUserProfile(obj);
        }

        [HttpGet("{RefId}/{docId}")]
        public DocumentUrlResponse GetDocumentUrl(string RefId, string docId)
        {
            DocumentUrlRequest documentRequest = new();
            documentRequest.CustomerId = "0";
            documentRequest.DocumentId = docId;
            documentRequest.RefId = RefId;
            return salesTicketBAL.GetDocumentUrl(documentRequest);
        }
    }

} 