using MongoDB.Driver;
using System.Runtime.Caching;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Helper;
using DataAccessLayer;
using PropertyLayers;
using Newtonsoft.Json;
using System.Text;

namespace Service.Helper
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class CommAuth : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext actionContext)
        {
            var context = actionContext.HttpContext;
            string? Source = context.Request != null ? context.Request.Headers["source"] : string.Empty;
            if (IsValidRequest(context.Request))
            {
                if (CoreCommonMethods.IsValidString(Source) && !IsAuthorize(context, context.Request, Source))
                {
                    actionContext.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "ErrorMessage", action = "NonAccess" }));
                }
            }
            else
            {
                actionContext.HttpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                actionContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "ErrorMessage", action = "NonAuthorised" }));
            }
        }

        public static bool IsValidRequest(HttpRequest request)
        {
            bool Result = false;
            try
            {
                string? Domain = request != null ? Convert.ToString(request.Host) : string.Empty;
                string? Source = request != null ? request.Headers["source"] : string.Empty;
                string? token = request != null ? request.Headers["Token"] : string.Empty;
                List<string> InvalidTokens = ["123456", "123456789"];

                if (!string.IsNullOrEmpty(Domain) && Domain.ToUpper().Contains("MATRIXCOREAPI"))
                {
                    Console.WriteLine("Domain " + Domain);
                    string? agentId = request != null ? request.Headers["AgentId"] : string.Empty;

                    var reqCookies = request != null ? request.Cookies["MatrixToken"] : string.Empty;
                    string _cookies = CoreCommonMethods.ReadCookies(reqCookies);

                    if (!string.IsNullOrEmpty(_cookies) && CoreCommonMethods.IsValidString(_cookies))
                    {
                        User? user = !string.IsNullOrEmpty(_cookies) ? JsonConvert.DeserializeObject<User>(_cookies) : null;
                        agentId = user != null ? user.UserId : string.Empty;
                        token = user != null ? user.AsteriskToken : string.Empty;

                        if (request != null)
                            request.Headers.Remove("AgentId");
                            request.Headers.Add("AgentId", agentId);

                    }

                    if (CoreCommonMethods.IsValidString(agentId) && CoreCommonMethods.IsValidString(token)
                        && !string.IsNullOrEmpty(token) && !string.IsNullOrEmpty(agentId) && !InvalidTokens.Contains(token))
                    {
                        string Key = $"{RedisCollection.PredictiveAgent()}:{agentId.Trim()}";
                        string FOSKey = $"{RedisCollection.AppKey()}:{agentId.Trim()}";

                        string? CacheToken = GetCacheToken(Key);

                        if (CoreCommonMethods.IsValidString(CacheToken) && !string.IsNullOrEmpty(CacheToken) && CacheToken.Trim() == token.Trim())
                            Result = true;
                        else
                        {
                            string MatrixToken = PredictiveAgentStatusRedis.GetMatrixToken(Convert.ToString(agentId));

                            if (CoreCommonMethods.IsValidString(MatrixToken) && MatrixToken == token)
                            {
                                Result = true;
                                CommonCache.GetOrInsertIntoCache(MatrixToken, Key, 8 * 60);
                            }
                            else if (!Result)
                            {
                                string AppToken = PredictiveAgentStatusRedis.GetAppToken(Convert.ToString(agentId));
                                if (CoreCommonMethods.IsValidString(AppToken) && AppToken == token)
                                {
                                    Result = true;
                                    CommonCache.GetOrInsertIntoCache(AppToken, Key, 8 * 60);
                                }
                            }
                        }
                        
                    }
                }
                ////////-----------------------  for Internal access -------------------------////////////////////
                else
                {

                    string? clientKey = request != null ? request.Headers["clientKey"] : string.Empty;
                    string? AuthKey = request != null ? request.Headers["authKey"] : string.Empty;
                    Console.WriteLine("Domain " + Domain + " Source " + Source + " clientKey " + clientKey + " AuthKey " + AuthKey);

                    if (CoreCommonMethods.IsValidString(AuthKey) && CoreCommonMethods.IsValidString(Source) && CoreCommonMethods.IsValidString(clientKey))
                    {
                        string Key = $"{RedisCollection.MongoConfig()}";
                        Dictionary<string, SysConfigData> _dictCollection = GetMongoCacheData(Key);


                        if (_dictCollection == null || _dictCollection.Count == 0)
                        {
                            _dictCollection = GetDictCollection();
                            CommonCache.GetOrInsertIntoCache(_dictCollection, Key, 12 * 60);
                        }

                        if (_dictCollection != null && _dictCollection.Count > 0)
                        {
                            SysConfigData SourceCollection = GetValidSource(_dictCollection, Source, Key);
                            if (SourceCollection != null)
                            {
                                string EncKey = !string.IsNullOrEmpty(SourceCollection.EncKey) ? SourceCollection.EncKey.ToString() : "";
                                string EncIV = !string.IsNullOrEmpty(SourceCollection.EncIV) ? SourceCollection.EncIV.ToString() : "";
                                request.Headers["EncKey"] = EncKey;
                                request.Headers["EncIV"] = EncIV;
                                Result = IsMatchData(SourceCollection, Source, AuthKey, clientKey);
                            }
                                
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Result = false;
                Console.WriteLine("Exception in IsValidRequest." + ex.ToString());
            }
            finally
            { 
                Console.WriteLine("Finally in IsValidRequest." + Result.ToString());
            }

            return Result;
        }

        public static string? GetCacheToken(string Key)
        {
            string? token = string.Empty;
            try
            {
                if (MemoryCache.Default[Key] is var memKey && memKey != null)
                {
                    token = Convert.ToString(memKey);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in getCacheDatain Commauth." + ex.ToString());
            }

            return token;
        }

        public static Dictionary<string, SysConfigData> GetMongoCacheData(string Key)
        {
            Dictionary<string, SysConfigData> obj = new();
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    ObjectCache CacheConfig = MemoryCache.Default;
                    obj = (Dictionary<string, SysConfigData>)CacheConfig.Get(Key);

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetMongoCacheData." + ex.ToString());
            }
            return obj;
        }

        public static Dictionary<string, SysConfigData> GetDictCollection()
        {
            Dictionary<string, SysConfigData> oMongoData = MongoDLL.GetConfiValueFromMongo();
            return oMongoData;
        }

        public static SysConfigData? GetValidSource(Dictionary<string, SysConfigData> _dictCollection, string Source, string Key)
        {
            SysConfigData? SourceCollection = null;    
            try
            {
                if (_dictCollection.ContainsKey(Source.ToLower()))
                {
                    SourceCollection = _dictCollection[Source.ToLower()];
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetValidSource." + ex.ToString());
            }
            return SourceCollection;
        }

        public static bool IsMatchData(SysConfigData SourceCollection, string Source, string AuthKey, string clientKey)
        {
            bool res = false;
            try
            {
                if (SourceCollection != null && SourceCollection.source.ToLower() == Source.ToLower() && SourceCollection.authKey == AuthKey && SourceCollection.clientKey == clientKey)
                    res = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in IsMatchData." + ex.ToString());
            }
            return res;
        }

        public bool IsAuthorize(HttpContext context, HttpRequest request, string Source)
        {

            bool IsValidRequest = false;
            StringBuilder sb = new();
            try
            {
                var endpoint = context.GetEndpoint() as RouteEndpoint;
                var CurrentActionMethod = endpoint?.RoutePattern?.RequiredValues?.Values?.FirstOrDefault();

                Dictionary<string, string>? SourceCollection = null;
                string? MethodURL = request != null ? request.Path.Value : string.Empty;
                string Key = $"{RedisCollection.ACLMongoConfig()}";


                sb.Append("MethodURL " + JsonConvert.SerializeObject(MethodURL) + "\r\n");
                sb.Append(" Source " + Source + "\r\n");

                if (MemoryCache.Default[Key] != null)
                {
                    sb.Append(" ,Enter in cache " + "\r\n");
                    ObjectCache CacheConfig = MemoryCache.Default;
                    SourceCollection = (Dictionary<string, string>)CacheConfig.Get(Key);
                }
                else
                    SourceCollection = GetACLDictCollection(Key);

                //----- Valid method found or not
                IsValidRequest = IsValidACLRequest(SourceCollection, Source, CurrentActionMethod.ToString());

                if (!IsValidRequest)
                    LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "IsAuthorize_MatrixFeedback", "MatrixFeedback", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 1, ex.ToString(), "IsAuthorize_MatrixFeedback", "MatrixFeedback", "CommAuth", JsonConvert.SerializeObject(sb), "", DateTime.Now, DateTime.Now);
            }
            return IsValidRequest;
        }

        public bool IsValidACLRequest(Dictionary<string, string> SourceCollection, string Source, string MethodURL)
        {
            bool res = false;

            string KeyToSearch = MethodURL.ToLower() + "_" + Source.ToLower();
            if (SourceCollection.ContainsKey(KeyToSearch))
            {
                res = true;
            }
            return res;
        }

       public Dictionary<string, string> GetACLDictCollection(string Key)
        {
            Dictionary<string, string> dictionary = [];

            var filter = Builders<ACLConfigData>.Filter.And(
                Builders<ACLConfigData>.Filter.Eq("isActive", true),
                Builders<ACLConfigData>.Filter.Eq("application", "matrixfeedback")
            );

            var projection = Builders<ACLConfigData>.Projection
                .Include(x => x.source)
                .Include(x => x.method)
                .Include(x => x.isActive);

            List<ACLConfigData> oMongoData = MongoDLL.GetACLMongoConfig(projection, filter);

            if (oMongoData != null && oMongoData.Count != 0)
            {
                dictionary = oMongoData
                    .Where(data => !string.IsNullOrEmpty(data.method) && !string.IsNullOrEmpty(data.source))
                    .GroupBy(data => $"{data.method.ToLower()}_{data.source.ToLower()}")
                    .ToDictionary(group => group.Key,group => group.First().method,StringComparer.OrdinalIgnoreCase);

                if (dictionary.Count != 0)
                {
                    CommonCache.GetOrInsertIntoCache(dictionary, Key, 12 * 60);
                }
            }

            return dictionary;
        }
    }
}

