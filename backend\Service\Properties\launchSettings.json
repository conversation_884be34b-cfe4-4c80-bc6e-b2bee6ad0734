﻿{
	"$schema": "https://json.schemastore.org/launchsettings.json",
	"iisSettings": {
		"windowsAuthentication": false,
		"anonymousAuthentication": true,
		"iisExpress": {
			"applicationUrl": "http://localhost:5207",
			"sslPort": 7219
		}
	},
	"profiles": {
		"IIS Express": {
			"commandName": "IISExpress",
			"launchBrowser": true,
			"launchUrl": "",
			"environmentVariables": {
				"ASPNETCORE_ENVIRONMENT": "Development"
			}
		},
		"Service": {
			"commandName": "Project",
			"launchBrowser": true,
			"launchUrl": "",
			"applicationUrl": "https://0.0.0.0:7219;http://0.0.0.0:5207",
			"environmentVariables": {
				"ASPNETCORE_ENVIRONMENT": "Development"
			}
		}
	}
}
