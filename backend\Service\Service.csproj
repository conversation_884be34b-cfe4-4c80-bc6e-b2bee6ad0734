<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BusinessLogicLayer\BusinessLogicLayer.csproj" />
		<ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
		<ProjectReference Include="..\LoggingHelper\LoggingHelper.csproj" />
	</ItemGroup>

</Project>
