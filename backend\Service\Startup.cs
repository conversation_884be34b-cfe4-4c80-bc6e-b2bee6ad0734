using System;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using BusinessLogicLayer;
using Microsoft.OpenApi.Models;
using MongoConfigProject;

namespace Service
{
    public class Startup
    {
        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public IConfiguration configRoot
        {
            get;
        }

        public Startup(IConfiguration configuration)
        {
            configRoot = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            string[] origin = "ValidOrigins".AppSettings().Split(",").ToArray();
           
            services.AddCors(options =>
            {
                options.AddPolicy(name: MyAllowSpecificOrigins,
                    builder =>
                    {
                        builder
                            .WithOrigins(origin)
                            .AllowAnyMethod()
                            .AllowAnyHeader()
                            .AllowCredentials();
                    });
            });

            services.AddHttpContextAccessor();
            
            services.AddControllers();
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { 
                    Title = "Feedback Service API", 
                    Version = "v1"
                });
            });
            
            // Add response compression
            services.Configure<GzipCompressionProviderOptions>
                    (options => options.Level = CompressionLevel.Optimal);
            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });

            // Register dependencies
            services.AddScoped<ISalesTicketBAL, SalesTicketBAL>();

            // Configure JSON options
            services.AddControllers().AddJsonOptions(options => 
            { 
                options.JsonSerializerOptions.PropertyNamingPolicy = null; 
                options.JsonSerializerOptions.IgnoreNullValues = true; 
            });
        }

        public void Configure(WebApplication app, IWebHostEnvironment env)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Feedback Service API V1");
                c.RoutePrefix = string.Empty;
            });

            app.UseResponseCompression();
            app.UseHttpsRedirection();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthorization();
            app.UseCors(MyAllowSpecificOrigins);
            app.MapControllers();
            app.Run();
        }
    }
} 