{"Environment": "UAT", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConfigKey": "MatrixFeedbackConfigValues", "ConnectionString": {"DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixFeedback;", "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixFeedback;", "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=MatrixFeedback;"}, "ProductDBConnection": {"DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixFeedback;", "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixFeedback;", "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;Application Name=MatrixFeedback;"}, "ReplicaConnectionString": {"DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixFeedback;", "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixFeedback;", "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=MatrixFeedback;"}, "MongoDB": {"OneLeadDBConnection": {"DEV": "******************************************************************", "UAT": "**************************************************************************************************************************************************************************************************************************************************************", "LIVE": "***********************************************************************************************************************************************************************************************************************************************"}, "OneLeadDB": "oneLeadDB"}, "Redis": {"CustRevisitConnection": {"DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379", "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379", "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"}, "RedisPassword": {"DEV": "hfS4X265vASb83F1LKags7BV", "UAT": "hfS4X265vASb83F1LKags7BV", "LIVE": "Fyt31c7B8gsS8B6a65vKJ"}}, "Kafka": {"BootstrapServers": {"DEV": "20.80.73.72:9092", "UAT": "20.80.73.72:9092", "LIVE": "10.80.25.42:9092,10.80.25.101:9092,10.80.25.140:9092"}, "UserName": {"DEV": "bmskfk", "UAT": "bmskfk", "LIVE": "bmskfk"}, "Password": {"DEV": "bkls76298764", "UAT": "bkls76298764", "LIVE": "bms76298764"}}}