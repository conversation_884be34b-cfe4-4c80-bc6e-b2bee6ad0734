using Confluent.Kafka;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Helper;
using PropertyLayers;

namespace Kafka
{

    public static class KafkaWrapper
    {
        private static KafkaConfig _kafkaConfig;

        public static async Task PushToKafka(LoggingRequest request)
        {
            string serializedData = string.Empty;
            DateTime reqtime = DateTime.Now;
            try
            {
                serializedData = JsonConvert.SerializeObject(request);

                _kafkaConfig = ReadKafkaConfig();
                if (_kafkaConfig != null)
                {
                    var producerConfig = new ProducerConfig
                    {
                        BootstrapServers = _kafkaConfig.BootstrapServers,
                        RequestTimeoutMs = _kafkaConfig.Timeout,
                        SocketTimeoutMs = _kafkaConfig.Timeout,
                        MessageSendMaxRetries = 2,
                        SaslUsername = _kafkaConfig.UserName,
                        SaslMechanism = SaslMechanism.Plain,
                        SecurityProtocol = SecurityProtocol.SaslPlaintext,
                        SaslPassword = _kafkaConfig.Password,
                        Acks = Acks.All
                    };

                    using (var producer = new ProducerBuilder<Null, string>(producerConfig).Build())
                    {
                        var response = await producer.ProduceAsync(_kafkaConfig.Topic, new Message<Null, string> { Value = serializedData });
                        producer.Flush(TimeSpan.FromSeconds(2));
                        if (response.Status != PersistenceStatus.Persisted)
                        {
                            Console.WriteLine($"PushToKafka . Request Time: {reqtime}, Response Timeout: {DateTime.Now}, Error: {"Message not persisted"}, Data: {serializedData}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(string.Format("Method PushToKafka>Request Time>{0}>Response Timeout>{1}> Error>{2}> data>{3}", reqtime, DateTime.Now, ex.ToString(), serializedData));
            }
        }

        private static KafkaConfig ReadKafkaConfig()
        {
            if (_kafkaConfig != null)
                return _kafkaConfig;
            else
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                string Enviornment = CoreCommonMethods.GetEnvironmentVar();

                _kafkaConfig = new KafkaConfig()
                {
                    BootstrapServers = con.GetSection("Kafka").GetSection("BootstrapServers").GetSection(Enviornment).Value,
                    UserName = con.GetSection("Kafka").GetSection("UserName").GetSection(Enviornment).Value,
                    Password = con.GetSection("Kafka").GetSection("Password").GetSection(Enviornment).Value,
                    Topic = "bms-logger-topic",
                    Timeout = 5000
                };
                return _kafkaConfig;
            }
        }
    }
} 