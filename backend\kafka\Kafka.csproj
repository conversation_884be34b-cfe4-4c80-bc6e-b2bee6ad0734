<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Confluent.Kafka" Version="2.2.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.4" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\DataHelper\DataHelper.csproj" />
		<ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj" />
	</ItemGroup>

</Project> 