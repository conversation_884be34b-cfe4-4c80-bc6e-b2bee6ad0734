using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace Kafka
{
    [DataContract]
    public class KafkaConfig
    {
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string BootstrapServers { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Password { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string Topic { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public int Timeout { get; set; }
    }
} 