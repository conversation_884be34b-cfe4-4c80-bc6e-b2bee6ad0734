[{"D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js": "1", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js": "2", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js": "3", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js": "4", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js": "5", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js": "6", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js": "7", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js": "8", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js": "9", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js": "10", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js": "11", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js": "12", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js": "13", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js": "14", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js": "15", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js": "16", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js": "17", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js": "18", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js": "19", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js": "20", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js": "21", "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js": "22"}, {"size": 664, "mtime": 1751279478399, "results": "23", "hashOfConfig": "24"}, {"size": 1127, "mtime": 1751266713093, "results": "25", "hashOfConfig": "24"}, {"size": 345, "mtime": 1750146475108, "results": "26", "hashOfConfig": "24"}, {"size": 2588, "mtime": 1750146475047, "results": "27", "hashOfConfig": "24"}, {"size": 6438, "mtime": 1750759167832, "results": "28", "hashOfConfig": "24"}, {"size": 30194, "mtime": 1751282490099, "results": "29", "hashOfConfig": "24"}, {"size": 30399, "mtime": 1751533810813, "results": "30", "hashOfConfig": "24"}, {"size": 10959, "mtime": 1751280208192, "results": "31", "hashOfConfig": "24"}, {"size": 9361, "mtime": 1751280245515, "results": "32", "hashOfConfig": "24"}, {"size": 3601, "mtime": 1751343655940, "results": "33", "hashOfConfig": "24"}, {"size": 13153, "mtime": 1751280227664, "results": "34", "hashOfConfig": "24"}, {"size": 4962, "mtime": 1750146474877, "results": "35", "hashOfConfig": "24"}, {"size": 10299, "mtime": 1751281122354, "results": "36", "hashOfConfig": "24"}, {"size": 39421, "mtime": 1750403903312, "results": "37", "hashOfConfig": "24"}, {"size": 3837, "mtime": 1750403903532, "results": "38", "hashOfConfig": "24"}, {"size": 2848, "mtime": 1750146476432, "results": "39", "hashOfConfig": "24"}, {"size": 11361, "mtime": 1751539581274, "results": "40", "hashOfConfig": "24"}, {"size": 2417, "mtime": 1750654173845, "results": "41", "hashOfConfig": "24"}, {"size": 688, "mtime": 1751524372106, "results": "42", "hashOfConfig": "24"}, {"size": 1996, "mtime": 1751539611706, "results": "43", "hashOfConfig": "24"}, {"size": 695, "mtime": 1751279359756, "results": "44", "hashOfConfig": "24"}, {"size": 13642, "mtime": 1751346124055, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s4k03p", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\index.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\App.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\Login.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\LandingPage.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MainLayout.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyTicketDetails.js", ["112", "113", "114"], ["115", "116", "117", "118", "119", "120", "121", "122"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\CreateFeedback.js", ["123", "124", "125", "126", "127"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\AllTickets.js", ["128"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanTickets.js", ["129"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyFeedback.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MySpanCreatedTicket.js", ["130", "131", "132", "133", "134", "135", "136", "137"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\EditProfile.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\MyAssignedTickets.js", ["138"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\TicketDetails.js", ["139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150"], ["151", "152", "153", "154", "155"], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\feedbackService.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\CommonHelper.js", ["156", "157"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\FeedbackTable.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\services\\api.service.js", ["158"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\config.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DataTableCard.js", ["159"], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\DashboardStats.js", [], [], "D:\\pb\\New folder\\matrixfeedback\\frontend\\src\\components\\common\\TicketPageHeader.js", [], [], {"ruleId": "160", "severity": 1, "message": "161", "line": 31, "column": 40, "nodeType": "162", "messageId": "163", "endLine": 31, "endColumn": 42}, {"ruleId": "160", "severity": 1, "message": "161", "line": 31, "column": 55, "nodeType": "162", "messageId": "163", "endLine": 31, "endColumn": 57}, {"ruleId": "164", "severity": 1, "message": "165", "line": 82, "column": 8, "nodeType": "166", "endLine": 82, "endColumn": 18, "suggestions": "167"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 45, "column": 43, "nodeType": "170", "messageId": "171", "endLine": 45, "endColumn": 45, "suppressions": "172"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 256, "column": 100, "nodeType": "170", "messageId": "171", "endLine": 256, "endColumn": 102, "suppressions": "173"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 256, "column": 126, "nodeType": "170", "messageId": "171", "endLine": 256, "endColumn": 128, "suppressions": "174"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 261, "column": 104, "nodeType": "177", "endLine": 261, "endColumn": 107, "suppressions": "178"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 269, "column": 101, "nodeType": "177", "endLine": 271, "endColumn": 268, "suppressions": "179"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 276, "column": 101, "nodeType": "177", "endLine": 279, "endColumn": 102, "suppressions": "180"}, {"ruleId": "168", "severity": 1, "message": "181", "line": 359, "column": 119, "nodeType": "170", "messageId": "171", "endLine": 359, "endColumn": 121, "suppressions": "182"}, {"ruleId": "168", "severity": 1, "message": "181", "line": 391, "column": 119, "nodeType": "170", "messageId": "171", "endLine": 391, "endColumn": 121, "suppressions": "183"}, {"ruleId": "184", "severity": 1, "message": "185", "line": 16, "column": 5, "nodeType": "186", "messageId": "187", "endLine": 16, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "188", "line": 28, "column": 19, "nodeType": "186", "messageId": "187", "endLine": 28, "endColumn": 33}, {"ruleId": "184", "severity": 1, "message": "189", "line": 42, "column": 50, "nodeType": "186", "messageId": "187", "endLine": 42, "endColumn": 56}, {"ruleId": "164", "severity": 1, "message": "190", "line": 62, "column": 8, "nodeType": "166", "endLine": 62, "endColumn": 10, "suggestions": "191"}, {"ruleId": "168", "severity": 1, "message": "181", "line": 152, "column": 27, "nodeType": "170", "messageId": "171", "endLine": 152, "endColumn": 29}, {"ruleId": "164", "severity": 1, "message": "190", "line": 62, "column": 8, "nodeType": "166", "endLine": 62, "endColumn": 10, "suggestions": "192"}, {"ruleId": "164", "severity": 1, "message": "190", "line": 60, "column": 8, "nodeType": "166", "endLine": 60, "endColumn": 10, "suggestions": "193"}, {"ruleId": "164", "severity": 1, "message": "190", "line": 64, "column": 8, "nodeType": "166", "endLine": 64, "endColumn": 10, "suggestions": "194"}, {"ruleId": "168", "severity": 1, "message": "181", "line": 171, "column": 46, "nodeType": "170", "messageId": "171", "endLine": 171, "endColumn": 48}, {"ruleId": "168", "severity": 1, "message": "169", "line": 195, "column": 44, "nodeType": "170", "messageId": "171", "endLine": 195, "endColumn": 46}, {"ruleId": "168", "severity": 1, "message": "169", "line": 203, "column": 41, "nodeType": "170", "messageId": "171", "endLine": 203, "endColumn": 43}, {"ruleId": "168", "severity": 1, "message": "169", "line": 210, "column": 59, "nodeType": "170", "messageId": "171", "endLine": 210, "endColumn": 61}, {"ruleId": "168", "severity": 1, "message": "181", "line": 214, "column": 30, "nodeType": "170", "messageId": "171", "endLine": 214, "endColumn": 32}, {"ruleId": "168", "severity": 1, "message": "181", "line": 214, "column": 62, "nodeType": "170", "messageId": "171", "endLine": 214, "endColumn": 64}, {"ruleId": "168", "severity": 1, "message": "169", "line": 216, "column": 62, "nodeType": "170", "messageId": "171", "endLine": 216, "endColumn": 64}, {"ruleId": "164", "severity": 1, "message": "190", "line": 55, "column": 8, "nodeType": "166", "endLine": 55, "endColumn": 10, "suggestions": "195"}, {"ruleId": "164", "severity": 1, "message": "165", "line": 37, "column": 8, "nodeType": "166", "endLine": 37, "endColumn": 18, "suggestions": "196"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 82, "column": 36, "nodeType": "170", "messageId": "171", "endLine": 82, "endColumn": 38}, {"ruleId": "168", "severity": 1, "message": "169", "line": 91, "column": 35, "nodeType": "170", "messageId": "171", "endLine": 91, "endColumn": 37}, {"ruleId": "168", "severity": 1, "message": "169", "line": 91, "column": 57, "nodeType": "170", "messageId": "171", "endLine": 91, "endColumn": 59}, {"ruleId": "168", "severity": 1, "message": "169", "line": 160, "column": 19, "nodeType": "170", "messageId": "171", "endLine": 160, "endColumn": 21}, {"ruleId": "168", "severity": 1, "message": "169", "line": 182, "column": 25, "nodeType": "170", "messageId": "171", "endLine": 182, "endColumn": 27}, {"ruleId": "168", "severity": 1, "message": "169", "line": 197, "column": 25, "nodeType": "170", "messageId": "171", "endLine": 197, "endColumn": 27}, {"ruleId": "168", "severity": 1, "message": "181", "line": 199, "column": 47, "nodeType": "170", "messageId": "171", "endLine": 199, "endColumn": 49}, {"ruleId": "168", "severity": 1, "message": "181", "line": 199, "column": 79, "nodeType": "170", "messageId": "171", "endLine": 199, "endColumn": 81}, {"ruleId": "168", "severity": 1, "message": "169", "line": 218, "column": 24, "nodeType": "170", "messageId": "171", "endLine": 218, "endColumn": 26}, {"ruleId": "168", "severity": 1, "message": "169", "line": 251, "column": 22, "nodeType": "170", "messageId": "171", "endLine": 251, "endColumn": 24}, {"ruleId": "168", "severity": 1, "message": "169", "line": 254, "column": 42, "nodeType": "170", "messageId": "171", "endLine": 254, "endColumn": 44}, {"ruleId": "175", "severity": 1, "message": "176", "line": 441, "column": 66, "nodeType": "177", "endLine": 441, "endColumn": 164, "suppressions": "197"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 442, "column": 66, "nodeType": "177", "endLine": 442, "endColumn": 164, "suppressions": "198"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 477, "column": 118, "nodeType": "177", "endLine": 477, "endColumn": 121, "suppressions": "199"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 484, "column": 101, "nodeType": "177", "endLine": 486, "endColumn": 256, "suppressions": "200"}, {"ruleId": "175", "severity": 1, "message": "176", "line": 491, "column": 101, "nodeType": "177", "endLine": 494, "endColumn": 102, "suppressions": "201"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 10, "column": 32, "nodeType": "170", "messageId": "171", "endLine": 10, "endColumn": 34}, {"ruleId": "168", "severity": 1, "message": "169", "line": 11, "column": 35, "nodeType": "170", "messageId": "171", "endLine": 11, "endColumn": 37}, {"ruleId": "184", "severity": 1, "message": "202", "line": 2, "column": 39, "nodeType": "186", "messageId": "187", "endLine": 2, "endColumn": 52}, {"ruleId": "184", "severity": 1, "message": "203", "line": 10, "column": 5, "nodeType": "186", "messageId": "187", "endLine": 10, "endColumn": 9}, "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'getTicketDetailsService'. Either include it or remove the dependency array.", "ArrayExpression", ["204"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", ["205"], ["206"], ["207"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", ["208"], ["209"], ["210"], "Expected '!==' and instead saw '!='.", ["211"], ["212"], "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'errors' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'GetAllProcess'. Either include it or remove the dependency array.", ["213"], ["214"], ["215"], ["216"], ["217"], ["218"], ["219"], ["220"], ["221"], ["222"], ["223"], "'clearAuthData' is defined but never used.", "'Chip' is defined but never used.", {"desc": "224", "fix": "225"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"desc": "228", "fix": "229"}, {"desc": "228", "fix": "230"}, {"desc": "228", "fix": "231"}, {"desc": "228", "fix": "232"}, {"desc": "228", "fix": "233"}, {"desc": "224", "fix": "234"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, {"kind": "226", "justification": "227"}, "Update the dependencies array to be: [getTicketDetailsService, ticketId]", {"range": "235", "text": "236"}, "directive", "", "Update the dependencies array to be: [GetAllProcess]", {"range": "237", "text": "238"}, {"range": "239", "text": "238"}, {"range": "240", "text": "238"}, {"range": "241", "text": "238"}, {"range": "242", "text": "238"}, {"range": "243", "text": "236"}, [2977, 2987], "[getTicketDetailsService, ticketId]", [1678, 1680], "[GetAllProcess]", [2160, 2162], [2053, 2055], [2233, 2235], [1884, 1886], [1782, 1792]]