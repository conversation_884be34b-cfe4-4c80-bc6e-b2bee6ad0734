{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DataTableCard.js\";\nimport React from 'react';\nimport { Box, Button, Card, CardContent, Typography, Stack } from '@mui/material';\nimport { GetApp as GetAppIcon } from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataTableCard = ({\n  feedbacks = [],\n  onExport,\n  tableType,\n  redirectPage,\n  tableTitle = \"Ticket Results\",\n  showExport = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    elevation: 0,\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [feedbacks && feedbacks.length > 0 && /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: tableTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 25\n        }, this), showExport && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(GetAppIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 44\n          }, this),\n          onClick: onExport,\n          children: \"Export Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(FeedbackTable, {\n          feedbacks: feedbacks,\n          type: tableType,\n          redirectPage: redirectPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 9\n  }, this);\n};\n_c = DataTableCard;\nexport default DataTableCard;\nvar _c;\n$RefreshReg$(_c, \"DataTableCard\");", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "GetApp", "GetAppIcon", "FeedbackTable", "jsxDEV", "_jsxDEV", "DataTableCard", "feedbacks", "onExport", "tableType", "redirectPage", "tableTitle", "showExport", "elevation", "children", "length", "direction", "spacing", "alignItems", "justifyContent", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DataTableCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n    <PERSON>,\n    But<PERSON>,\n    Card,\n    CardContent,\n    Typography,\n    Stack\n} from '@mui/material';\nimport {\n    GetApp as GetAppIcon\n} from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\nconst DataTableCard = ({\n    feedbacks = [],\n    onExport,\n    tableType,\n    redirectPage,\n    tableTitle = \"Ticket Results\",\n    showExport = true\n}) => {\n    return (\n        <Card elevation={0}>\n            <CardContent>\n                {feedbacks && feedbacks.length > 0 && (\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\n                        <Typography variant=\"h6\">\n                            {tableTitle}\n                        </Typography>\n                        {showExport && (\n                            <Button\n                                variant=\"outlined\"\n                                startIcon={<GetAppIcon />}\n                                onClick={onExport}\n                            >\n                                Export Data\n                            </Button>\n                        )}\n                    </Stack>\n                )}\n                <Box>\n                    <FeedbackTable\n                        feedbacks={feedbacks}\n                        type={tableType}\n                        redirectPage={redirectPage}\n                    />\n                </Box>\n            </CardContent>\n        </Card>\n    );\n};\n\nexport default DataTableCard; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACF,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,aAAa,GAAGA,CAAC;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG,gBAAgB;EAC7BC,UAAU,GAAG;AACjB,CAAC,KAAK;EACF,oBACIP,OAAA,CAACR,IAAI;IAACgB,SAAS,EAAE,CAAE;IAAAC,QAAA,eACfT,OAAA,CAACP,WAAW;MAAAgB,QAAA,GACPP,SAAS,IAAIA,SAAS,CAACQ,MAAM,GAAG,CAAC,iBAC9BV,OAAA,CAACL,KAAK;QAACgB,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,gBACxFT,OAAA,CAACN,UAAU;UAACsB,OAAO,EAAC,IAAI;UAAAP,QAAA,EACnBH;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACZb,UAAU,iBACPP,OAAA,CAACT,MAAM;UACHyB,OAAO,EAAC,UAAU;UAClBK,SAAS,eAAErB,OAAA,CAACH,UAAU;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BE,OAAO,EAAEnB,QAAS;UAAAM,QAAA,EACrB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACV,eACDpB,OAAA,CAACV,GAAG;QAAAmB,QAAA,eACAT,OAAA,CAACF,aAAa;UACVI,SAAS,EAAEA,SAAU;UACrBqB,IAAI,EAAEnB,SAAU;UAChBC,YAAY,EAAEA;QAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEf,CAAC;AAACI,EAAA,GArCIvB,aAAa;AAuCnB,eAAeA,aAAa;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}