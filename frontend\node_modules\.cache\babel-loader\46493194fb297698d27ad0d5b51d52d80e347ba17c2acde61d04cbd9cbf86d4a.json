{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DataTableCard.js\";\nimport React from 'react';\nimport { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography, Grow, Stack, Chip } from '@mui/material';\nimport { GetApp as GetAppIcon } from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataTableCard = ({\n  feedbacks = [],\n  onExport,\n  tableType,\n  redirectPage,\n  tableTitle = \"Ticket Results\",\n  showExport = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(Grow, {\n    in: true,\n    timeout: 1200,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      sx: {\n        borderRadius: 2,\n        border: '1px solid',\n        borderColor: 'divider',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n        background: 'background.paper',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 0,\n          '&:last-child': {\n            paddingBottom: 0\n          }\n        },\n        children: [feedbacks && feedbacks.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            borderBottom: '1px solid',\n            borderColor: 'divider',\n            background: 'grey.50'\n          },\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: 'text.primary',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [tableTitle, /*#__PURE__*/_jsxDEV(Chip, {\n                label: feedbacks.length,\n                size: \"small\",\n                sx: {\n                  bgcolor: 'primary.main',\n                  color: 'white',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 33\n            }, this), showExport && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(GetAppIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 52\n              }, this),\n              onClick: onExport,\n              sx: {\n                textTransform: 'none',\n                borderRadius: 1.5,\n                px: 2,\n                '&:hover': {\n                  bgcolor: 'primary.main',\n                  color: 'white'\n                }\n              },\n              children: \"Export Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            minHeight: feedbacks.length === 0 ? '200px' : 'auto',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: /*#__PURE__*/_jsxDEV(FeedbackTable, {\n            feedbacks: feedbacks,\n            type: tableType,\n            redirectPage: redirectPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_c = DataTableCard;\nexport default DataTableCard;\nvar _c;\n$RefreshReg$(_c, \"DataTableCard\");", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grow", "<PERSON><PERSON>", "Chip", "GetApp", "GetAppIcon", "FeedbackTable", "jsxDEV", "_jsxDEV", "DataTableCard", "feedbacks", "onExport", "tableType", "redirectPage", "tableTitle", "showExport", "in", "timeout", "children", "elevation", "sx", "borderRadius", "border", "borderColor", "boxShadow", "background", "overflow", "p", "paddingBottom", "length", "borderBottom", "direction", "spacing", "alignItems", "justifyContent", "variant", "fontWeight", "color", "display", "gap", "label", "size", "bgcolor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "textTransform", "px", "minHeight", "flexDirection", "type", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DataTableCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n    <PERSON>,\n    <PERSON><PERSON>,\n    Card,\n    CardContent,\n    Typography,\n    Grow,\n    Stack,\n    Chip\n} from '@mui/material';\nimport {\n    GetApp as GetAppIcon\n} from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\n\nconst DataTableCard = ({\n    feedbacks = [],\n    onExport,\n    tableType,\n    redirectPage,\n    tableTitle = \"Ticket Results\",\n    showExport = true\n}) => {\n    return (\n        <Grow in timeout={1200}>\n            <Card\n                elevation={0}\n                sx={{\n                    borderRadius: 2,\n                    border: '1px solid',\n                    borderColor: 'divider',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    background: 'background.paper',\n                    overflow: 'hidden'\n                }}\n            >\n                <CardContent \n                    sx={{ \n                        p: 0,\n                        '&:last-child': { \n                            paddingBottom: 0 \n                        }\n                    }}\n                >\n                    {feedbacks && feedbacks.length > 0 && (\n                        <Box\n                            sx={{\n                                p: 2,\n                                borderBottom: '1px solid',\n                                borderColor: 'divider',\n                                background: 'grey.50'\n                            }}\n                        >\n                            <Stack \n                                direction=\"row\" \n                                spacing={2} \n                                alignItems=\"center\" \n                                justifyContent=\"space-between\"\n                            >\n                                <Typography \n                                    variant=\"h6\" \n                                    sx={{\n                                        fontWeight: 600,\n                                        color: 'text.primary',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: 1\n                                    }}\n                                >\n                                    {tableTitle}\n                                    <Chip \n                                        label={feedbacks.length} \n                                        size=\"small\" \n                                        sx={{\n                                            bgcolor: 'primary.main',\n                                            color: 'white',\n                                            fontWeight: 'bold'\n                                        }}\n                                    />\n                                </Typography>\n\n                                {showExport && (\n                                    <Button\n                                        variant=\"outlined\"\n                                        startIcon={<GetAppIcon />}\n                                        onClick={onExport}\n                                        sx={{\n                                            textTransform: 'none',\n                                            borderRadius: 1.5,\n                                            px: 2,\n                                            '&:hover': {\n                                                bgcolor: 'primary.main',\n                                                color: 'white'\n                                            }\n                                        }}\n                                    >\n                                        Export Data\n                                    </Button>\n                                )}\n                            </Stack>\n                        </Box>\n                    )}\n                    \n                    <Box sx={{ \n                        minHeight: feedbacks.length === 0 ? '200px' : 'auto',\n                        display: 'flex',\n                        flexDirection: 'column'\n                    }}>\n                        <FeedbackTable\n                            feedbacks={feedbacks}\n                            type={tableType}\n                            redirectPage={redirectPage}\n                        />\n                    </Box>\n                </CardContent>\n            </Card>\n        </Grow>\n    );\n};\n\nexport default DataTableCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,QACD,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,kBAAkB;;AAE5C;AACA,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,aAAa,GAAGA,CAAC;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG,gBAAgB;EAC7BC,UAAU,GAAG;AACjB,CAAC,KAAK;EACF,oBACIP,OAAA,CAACP,IAAI;IAACe,EAAE;IAACC,OAAO,EAAE,IAAK;IAAAC,QAAA,eACnBV,OAAA,CAACV,IAAI;MACDqB,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACAC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE,WAAW;QACnBC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,2BAA2B;QACtCC,UAAU,EAAE,kBAAkB;QAC9BC,QAAQ,EAAE;MACd,CAAE;MAAAR,QAAA,eAEFV,OAAA,CAACT,WAAW;QACRqB,EAAE,EAAE;UACAO,CAAC,EAAE,CAAC;UACJ,cAAc,EAAE;YACZC,aAAa,EAAE;UACnB;QACJ,CAAE;QAAAV,QAAA,GAEDR,SAAS,IAAIA,SAAS,CAACmB,MAAM,GAAG,CAAC,iBAC9BrB,OAAA,CAACZ,GAAG;UACAwB,EAAE,EAAE;YACAO,CAAC,EAAE,CAAC;YACJG,YAAY,EAAE,WAAW;YACzBP,WAAW,EAAE,SAAS;YACtBE,UAAU,EAAE;UAChB,CAAE;UAAAP,QAAA,eAEFV,OAAA,CAACN,KAAK;YACF6B,SAAS,EAAC,KAAK;YACfC,OAAO,EAAE,CAAE;YACXC,UAAU,EAAC,QAAQ;YACnBC,cAAc,EAAC,eAAe;YAAAhB,QAAA,gBAE9BV,OAAA,CAACR,UAAU;cACPmC,OAAO,EAAC,IAAI;cACZf,EAAE,EAAE;gBACAgB,UAAU,EAAE,GAAG;gBACfC,KAAK,EAAE,cAAc;gBACrBC,OAAO,EAAE,MAAM;gBACfL,UAAU,EAAE,QAAQ;gBACpBM,GAAG,EAAE;cACT,CAAE;cAAArB,QAAA,GAEDJ,UAAU,eACXN,OAAA,CAACL,IAAI;gBACDqC,KAAK,EAAE9B,SAAS,CAACmB,MAAO;gBACxBY,IAAI,EAAC,OAAO;gBACZrB,EAAE,EAAE;kBACAsB,OAAO,EAAE,cAAc;kBACvBL,KAAK,EAAE,OAAO;kBACdD,UAAU,EAAE;gBAChB;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAEZ/B,UAAU,iBACPP,OAAA,CAACX,MAAM;cACHsC,OAAO,EAAC,UAAU;cAClBY,SAAS,eAAEvC,OAAA,CAACH,UAAU;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BE,OAAO,EAAErC,QAAS;cAClBS,EAAE,EAAE;gBACA6B,aAAa,EAAE,MAAM;gBACrB5B,YAAY,EAAE,GAAG;gBACjB6B,EAAE,EAAE,CAAC;gBACL,SAAS,EAAE;kBACPR,OAAO,EAAE,cAAc;kBACvBL,KAAK,EAAE;gBACX;cACJ,CAAE;cAAAnB,QAAA,EACL;YAED;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACR,eAEDtC,OAAA,CAACZ,GAAG;UAACwB,EAAE,EAAE;YACL+B,SAAS,EAAEzC,SAAS,CAACmB,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM;YACpDS,OAAO,EAAE,MAAM;YACfc,aAAa,EAAE;UACnB,CAAE;UAAAlC,QAAA,eACEV,OAAA,CAACF,aAAa;YACVI,SAAS,EAAEA,SAAU;YACrB2C,IAAI,EAAEzC,SAAU;YAChBC,YAAY,EAAEA;UAAa;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACQ,EAAA,GAvGI7C,aAAa;AAyGnB,eAAeA,aAAa;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}