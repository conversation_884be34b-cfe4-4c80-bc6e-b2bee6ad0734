{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DataTableCard.js\";\nimport React from 'react';\nimport { Box, Button, Card, CardContent, Typography, Grow, Stack } from '@mui/material';\nimport { GetApp as GetAppIcon } from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataTableCard = ({\n  feedbacks = [],\n  onExport,\n  tableType,\n  redirectPage,\n  tableTitle = \"Ticket Results\",\n  showExport = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(Grow, {\n    in: true,\n    timeout: 1200,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      className: \"data-table-card\",\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        className: \"table-card-content\",\n        children: [feedbacks && feedbacks.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          className: \"table-header-section\",\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              className: \"table-title\",\n              children: tableTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 33\n            }, this), showExport && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(GetAppIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 52\n              }, this),\n              onClick: onExport,\n              className: \"export-btn\",\n              children: \"Export Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: `table-content ${feedbacks.length === 0 ? 'empty' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(FeedbackTable, {\n            feedbacks: feedbacks,\n            type: tableType,\n            redirectPage: redirectPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 9\n  }, this);\n};\n_c = DataTableCard;\nexport default DataTableCard;\nvar _c;\n$RefreshReg$(_c, \"DataTableCard\");", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grow", "<PERSON><PERSON>", "GetApp", "GetAppIcon", "FeedbackTable", "jsxDEV", "_jsxDEV", "DataTableCard", "feedbacks", "onExport", "tableType", "redirectPage", "tableTitle", "showExport", "in", "timeout", "children", "elevation", "className", "length", "direction", "spacing", "alignItems", "justifyContent", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DataTableCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n    <PERSON>,\n    But<PERSON>,\n    Card,\n    CardContent,\n    Typography,\n    Grow,\n    Stack\n} from '@mui/material';\nimport {\n    GetApp as GetAppIcon\n} from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\n\nconst DataTableCard = ({\n    feedbacks = [],\n    onExport,\n    tableType,\n    redirectPage,\n    tableTitle = \"Ticket Results\",\n    showExport = true\n}) => {\n    return (\n        <Grow in timeout={1200}>\n            <Card\n                elevation={0}\n                className=\"data-table-card\"\n            >\n                <CardContent className=\"table-card-content\">\n                    {feedbacks && feedbacks.length > 0 && (\n                        <Box className=\"table-header-section\">\n                            <Stack \n                                direction=\"row\" \n                                spacing={2} \n                                alignItems=\"center\" \n                                justifyContent=\"space-between\"\n                            >\n                                <Typography \n                                    variant=\"h6\" \n                                    className=\"table-title\"\n                                >\n                                    {tableTitle}\n                                </Typography>\n\n                                {showExport && (\n                                    <Button\n                                        variant=\"outlined\"\n                                        startIcon={<GetAppIcon />}\n                                        onClick={onExport}\n                                        className=\"export-btn\"\n                                    >\n                                        Export Data\n                                    </Button>\n                                )}\n                            </Stack>\n                        </Box>\n                    )}\n                    \n                    <Box className={`table-content ${feedbacks.length === 0 ? 'empty' : ''}`}>\n                        <FeedbackTable\n                            feedbacks={feedbacks}\n                            type={tableType}\n                            redirectPage={redirectPage}\n                        />\n                    </Box>\n                </CardContent>\n            </Card>\n        </Grow>\n    );\n};\n\nexport default DataTableCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,KAAK,QACF,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,QACjB,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,kBAAkB;;AAE5C;AACA,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,aAAa,GAAGA,CAAC;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG,gBAAgB;EAC7BC,UAAU,GAAG;AACjB,CAAC,KAAK;EACF,oBACIP,OAAA,CAACN,IAAI;IAACc,EAAE;IAACC,OAAO,EAAE,IAAK;IAAAC,QAAA,eACnBV,OAAA,CAACT,IAAI;MACDoB,SAAS,EAAE,CAAE;MACbC,SAAS,EAAC,iBAAiB;MAAAF,QAAA,eAE3BV,OAAA,CAACR,WAAW;QAACoB,SAAS,EAAC,oBAAoB;QAAAF,QAAA,GACtCR,SAAS,IAAIA,SAAS,CAACW,MAAM,GAAG,CAAC,iBAC9Bb,OAAA,CAACX,GAAG;UAACuB,SAAS,EAAC,sBAAsB;UAAAF,QAAA,eACjCV,OAAA,CAACL,KAAK;YACFmB,SAAS,EAAC,KAAK;YACfC,OAAO,EAAE,CAAE;YACXC,UAAU,EAAC,QAAQ;YACnBC,cAAc,EAAC,eAAe;YAAAP,QAAA,gBAE9BV,OAAA,CAACP,UAAU;cACPyB,OAAO,EAAC,IAAI;cACZN,SAAS,EAAC,aAAa;cAAAF,QAAA,EAEtBJ;YAAU;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEZf,UAAU,iBACPP,OAAA,CAACV,MAAM;cACH4B,OAAO,EAAC,UAAU;cAClBK,SAAS,eAAEvB,OAAA,CAACH,UAAU;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BE,OAAO,EAAErB,QAAS;cAClBS,SAAS,EAAC,YAAY;cAAAF,QAAA,EACzB;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACR,eAEDtB,OAAA,CAACX,GAAG;UAACuB,SAAS,EAAE,iBAAiBV,SAAS,CAACW,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,EAAG;UAAAH,QAAA,eACrEV,OAAA,CAACF,aAAa;YACVI,SAAS,EAAEA,SAAU;YACrBuB,IAAI,EAAErB,SAAU;YAChBC,YAAY,EAAEA;UAAa;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACI,EAAA,GAvDIzB,aAAa;AAyDnB,eAAeA,aAAa;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}