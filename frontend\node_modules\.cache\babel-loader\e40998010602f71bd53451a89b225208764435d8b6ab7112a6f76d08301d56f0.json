{"ast": null, "code": "var _jsxFileName = \"D:\\\\pb\\\\New folder\\\\matrixfeedback\\\\frontend\\\\src\\\\components\\\\common\\\\DataTableCard.js\";\nimport React from 'react';\nimport { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography, Grow, Stack, Chip } from '@mui/material';\nimport { GetApp as GetAppIcon, TableChart as TableChartIcon, CloudDownload as CloudDownloadIcon } from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DataTableCard = ({\n  feedbacks = [],\n  onExport,\n  tableType,\n  redirectPage,\n  tableTitle = \"Ticket Results\",\n  showExport = true\n}) => {\n  const recordCount = (feedbacks === null || feedbacks === void 0 ? void 0 : feedbacks.length) || 0;\n  const hasRecords = recordCount > 0;\n  return /*#__PURE__*/_jsxDEV(Grow, {\n    in: true,\n    timeout: 1200,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      className: \"modern-data-table-card\",\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        className: \"modern-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"table-header-section\",\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"flex-start\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"header-content-box\",\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                className: \"table-title\",\n                children: [/*#__PURE__*/_jsxDEV(TableChartIcon, {\n                  className: \"title-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 37\n                }, this), tableTitle]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 29\n            }, this), showExport && hasRecords && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(CloudDownloadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 48\n              }, this),\n              onClick: onExport,\n              className: \"export-button modern-btn\",\n              children: \"Export Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"table-content-wrapper\",\n          children: hasRecords ? /*#__PURE__*/_jsxDEV(FeedbackTable, {\n            feedbacks: feedbacks,\n            type: tableType,\n            redirectPage: redirectPage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            className: \"empty-state-box\",\n            children: [/*#__PURE__*/_jsxDEV(TableChartIcon, {\n              className: \"empty-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: \"empty-text\",\n              children: \"No tickets found matching your criteria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              className: \"empty-subtext\",\n              children: \"Try adjusting your filters or search terms\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 9\n  }, this);\n};\n_c = DataTableCard;\nexport default DataTableCard;\nvar _c;\n$RefreshReg$(_c, \"DataTableCard\");", "map": {"version": 3, "names": ["React", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grow", "<PERSON><PERSON>", "Chip", "GetApp", "GetAppIcon", "Table<PERSON>hart", "TableChartIcon", "CloudDownload", "CloudDownloadIcon", "FeedbackTable", "jsxDEV", "_jsxDEV", "DataTableCard", "feedbacks", "onExport", "tableType", "redirectPage", "tableTitle", "showExport", "recordCount", "length", "hasRecords", "in", "timeout", "children", "elevation", "className", "direction", "spacing", "alignItems", "justifyContent", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "type", "_c", "$RefreshReg$"], "sources": ["D:/pb/New folder/matrixfeedback/frontend/src/components/common/DataTableCard.js"], "sourcesContent": ["import React from 'react';\nimport {\n    <PERSON>,\n    <PERSON><PERSON>,\n    Card,\n    CardContent,\n    Typography,\n    Grow,\n    Stack,\n    Chip\n} from '@mui/material';\nimport {\n    GetApp as GetAppIcon,\n    TableChart as TableChartIcon,\n    CloudDownload as CloudDownloadIcon\n} from '@mui/icons-material';\nimport FeedbackTable from '../FeedbackTable';\n\n// Import SCSS files for styling\nimport '../../styles/main.scss';\n\nconst DataTableCard = ({\n    feedbacks = [],\n    onExport,\n    tableType,\n    redirectPage,\n    tableTitle = \"Ticket Results\",\n    showExport = true\n}) => {\n    const recordCount = feedbacks?.length || 0;\n    const hasRecords = recordCount > 0;\n\n    return (\n        <Grow in timeout={1200}>\n            <Card elevation={0} className=\"modern-data-table-card\">\n                <CardContent className=\"modern-card-content\">\n                    <Box className=\"table-header-section\">\n                        <Stack direction=\"row\" spacing={2} alignItems=\"flex-start\" justifyContent=\"space-between\">\n                            <Box className=\"header-content-box\">\n                                <Typography variant=\"h5\" className=\"table-title\">\n                                    <TableChartIcon className=\"title-icon\" />\n                                    {tableTitle}\n                                </Typography>\n                            </Box>\n\n                            {showExport && hasRecords && (\n                                <Button\n                                    variant=\"contained\"\n                                    startIcon={<CloudDownloadIcon />}\n                                    onClick={onExport}\n                                    className=\"export-button modern-btn\"\n                                >\n                                    Export Data\n                                </Button>\n                            )}\n                        </Stack>\n                    </Box>\n\n                    <Box className=\"table-content-wrapper\">\n                        {hasRecords ? (\n                            <FeedbackTable\n                                feedbacks={feedbacks}\n                                type={tableType}\n                                redirectPage={redirectPage}\n                            />\n                        ) : (\n                            <Box className=\"empty-state-box\">\n                                <TableChartIcon className=\"empty-icon\" />\n                                <Typography className=\"empty-text\">\n                                    No tickets found matching your criteria\n                                </Typography>\n                                <Typography variant=\"body2\" className=\"empty-subtext\">\n                                    Try adjusting your filters or search terms\n                                </Typography>\n                            </Box>\n                        )}\n                    </Box>\n                </CardContent>\n            </Card>\n        </Grow>\n    );\n};\n\nexport default DataTableCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,IAAI,QACD,eAAe;AACtB,SACIC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,QAC/B,qBAAqB;AAC5B,OAAOC,aAAa,MAAM,kBAAkB;;AAE5C;AACA,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,aAAa,GAAGA,CAAC;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,SAAS;EACTC,YAAY;EACZC,UAAU,GAAG,gBAAgB;EAC7BC,UAAU,GAAG;AACjB,CAAC,KAAK;EACF,MAAMC,WAAW,GAAG,CAAAN,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEO,MAAM,KAAI,CAAC;EAC1C,MAAMC,UAAU,GAAGF,WAAW,GAAG,CAAC;EAElC,oBACIR,OAAA,CAACX,IAAI;IAACsB,EAAE;IAACC,OAAO,EAAE,IAAK;IAAAC,QAAA,eACnBb,OAAA,CAACd,IAAI;MAAC4B,SAAS,EAAE,CAAE;MAACC,SAAS,EAAC,wBAAwB;MAAAF,QAAA,eAClDb,OAAA,CAACb,WAAW;QAAC4B,SAAS,EAAC,qBAAqB;QAAAF,QAAA,gBACxCb,OAAA,CAAChB,GAAG;UAAC+B,SAAS,EAAC,sBAAsB;UAAAF,QAAA,eACjCb,OAAA,CAACV,KAAK;YAAC0B,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACC,UAAU,EAAC,YAAY;YAACC,cAAc,EAAC,eAAe;YAAAN,QAAA,gBACrFb,OAAA,CAAChB,GAAG;cAAC+B,SAAS,EAAC,oBAAoB;cAAAF,QAAA,eAC/Bb,OAAA,CAACZ,UAAU;gBAACgC,OAAO,EAAC,IAAI;gBAACL,SAAS,EAAC,aAAa;gBAAAF,QAAA,gBAC5Cb,OAAA,CAACL,cAAc;kBAACoB,SAAS,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxClB,UAAU;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAELjB,UAAU,IAAIG,UAAU,iBACrBV,OAAA,CAACf,MAAM;cACHmC,OAAO,EAAC,WAAW;cACnBK,SAAS,eAAEzB,OAAA,CAACH,iBAAiB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACjCE,OAAO,EAAEvB,QAAS;cAClBY,SAAS,EAAC,0BAA0B;cAAAF,QAAA,EACvC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAENxB,OAAA,CAAChB,GAAG;UAAC+B,SAAS,EAAC,uBAAuB;UAAAF,QAAA,EACjCH,UAAU,gBACPV,OAAA,CAACF,aAAa;YACVI,SAAS,EAAEA,SAAU;YACrByB,IAAI,EAAEvB,SAAU;YAChBC,YAAY,EAAEA;UAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,gBAEFxB,OAAA,CAAChB,GAAG;YAAC+B,SAAS,EAAC,iBAAiB;YAAAF,QAAA,gBAC5Bb,OAAA,CAACL,cAAc;cAACoB,SAAS,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCxB,OAAA,CAACZ,UAAU;cAAC2B,SAAS,EAAC,YAAY;cAAAF,QAAA,EAAC;YAEnC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxB,OAAA,CAACZ,UAAU;cAACgC,OAAO,EAAC,OAAO;cAACL,SAAS,EAAC,eAAe;cAAAF,QAAA,EAAC;YAEtD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEf,CAAC;AAACI,EAAA,GA5DI3B,aAAa;AA8DnB,eAAeA,aAAa;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}