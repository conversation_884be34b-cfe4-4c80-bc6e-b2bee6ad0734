{"name": "feedback-ui", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.5.1", "@mui/icons-material": "^6.4.12", "@mui/material": "^6.4.12", "@mui/x-date-pickers": "^8.6.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "alasql": "^4.6.6", "axios": "^1.6.2", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "jquery": "^3.5.1", "popper.js": "^1.16.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-toastify": "^9.1.3", "typeface-open-sans": "^1.1.13", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"sass": "^1.89.2"}}