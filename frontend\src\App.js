import React from 'react';
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
// import 'bootstrap/dist/css/bootstrap.min.css';
// import '@fortawesome/fontawesome-free/css/all.min.css';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import LandingPage from './components/LandingPage';
import LoginPage from './components/Login';
import MainLayout from './components/MainLayout';
import { ThemeProvider } from '@mui/material/styles';

function App() {

	return (

		
		<Router>
			<Routes>
				<Route path="/" element={<LandingPage />} />
				<Route path="/LandingPage" element={<LandingPage />} />
				<Route path="/LandingPage/:type" element={<LandingPage />} />
				<Route path="/LandingPage/:type/:ticketId" element={<LandingPage />} />
				<Route path="/LandingPage/:ticketId/:type" element={<LandingPage />} />
				<Route path="/login" element={<LoginPage />} />
				<Route path="/*" element={<MainLayout />} />
			</Routes>
			<ToastContainer position="top-right" autoClose={3000} />
		</Router>
		
	);
}

export default App; 