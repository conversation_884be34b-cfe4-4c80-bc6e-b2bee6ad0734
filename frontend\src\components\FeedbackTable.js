import React from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    Box,
    Typography
} from '@mui/material';
import { formatDate } from '../services/CommonHelper';
import { Link } from 'react-router-dom';

const FeedbackTable = ({ feedbacks, type = 0, redirectPage }) => {

    // Helper function to get status class
    const getStatusClass = (status) => {
        const statusLower = status?.toLowerCase() || '';
        if (statusLower.includes('new')) return 'status-new';
        if (statusLower.includes('open')) return 'status-open';
        if (statusLower.includes('resolved')) return 'status-resolved';
        if (statusLower.includes('closed')) return 'status-closed';
        if (statusLower.includes('pending')) return 'status-pending';
        return 'status-new';
    };

    return (
        <Box className="creative-table-wrapper">
            {feedbacks.length > 0 ? (
                <TableContainer component={Paper} className="creative-table-container">
                    <Table className="creative-table">
                        <TableHead className="creative-table-head">
                            <TableRow className="creative-header-row">
                                <TableCell className="creative-header-cell creative-header-cell-first">
                                    <span className="header-text">FeedbackId</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                                {[5].includes(type) && (
                                    <>
                                        <TableCell className="creative-header-cell">
                                            <span className="header-text">Emp Name</span>
                                            <div className="header-decoration"></div>
                                        </TableCell>
                                        <TableCell className="creative-header-cell">
                                            <span className="header-text">Emp ID</span>
                                            <div className="header-decoration"></div>
                                        </TableCell>
                                    </>
                                )}
                                <TableCell className="creative-header-cell">
                                    <span className="header-text">Created On</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                                {[2,3,5].includes(type) && (
                                    <>
                                        <TableCell className="creative-header-cell">
                                            <span className="header-text">Matrix Role</span>
                                            <div className="header-decoration"></div>
                                        </TableCell>
                                        <TableCell className="creative-header-cell">
                                            <span className="header-text">BU</span>
                                            <div className="header-decoration"></div>
                                        </TableCell>
                                    </>
                                )}
                                {[3,4,5].includes(type) && (
                                    <TableCell className="creative-header-cell">
                                        <span className="header-text">AssignTo</span>
                                        <div className="header-decoration"></div>
                                    </TableCell>
                                )}
                                <TableCell className="creative-header-cell">
                                    <span className="header-text">Process</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                                <TableCell className="creative-header-cell">
                                    <span className="header-text">SubProcess</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                                <TableCell className="creative-header-cell">
                                    <span className="header-text">Status</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                                <TableCell className="creative-header-cell creative-header-cell-last">
                                    <span className="header-text">Updated On</span>
                                    <div className="header-decoration"></div>
                                </TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody className="creative-table-body">
                            {feedbacks.map((feedback, index) => (
                                <TableRow key={feedback.TicketID} className={`creative-body-row ${index % 2 === 0 ? 'row-even' : 'row-odd'}`}>
                                    <TableCell className="creative-body-cell creative-body-cell-first">
                                        <Link to={`${redirectPage}${feedback.TicketID}`} className="creative-feedback-link">
                                            <span className="link-text">{feedback.TicketDisplayID}</span>
                                            <div className="link-shimmer"></div>
                                            <div className="link-glow"></div>
                                        </Link>
                                    </TableCell>
                                    {[5].includes(type) && (
                                        <>
                                            <TableCell className="creative-body-cell">
                                                <span className="cell-content">{feedback.CreatedByDetails.Name != null ? feedback.CreatedByDetails.Name : ''}</span>
                                            </TableCell>
                                            <TableCell className="creative-body-cell">
                                                <span className="cell-content employee-id">{feedback.CreatedByDetails.EmployeeID != null ? feedback.CreatedByDetails.EmployeeID : ''}</span>
                                            </TableCell>
                                        </>
                                    )}
                                    <TableCell className="creative-body-cell">
                                        <span className="cell-content date-content">{formatDate(feedback.CreatedOn)}</span>
                                    </TableCell>
                                    {[2,3,5].includes(type) && (
                                        <>
                                            <TableCell className="creative-body-cell">
                                                <span className="cell-content role-content">{feedback.MatrixRole}</span>
                                            </TableCell>
                                            <TableCell className="creative-body-cell">
                                                <span className="cell-content bu-content">{feedback.BU}</span>
                                            </TableCell>
                                        </>
                                    )}
                                    {[3,4,5].includes(type) && (
                                        <TableCell className="creative-body-cell">
                                            <span className="cell-content assign-content">
                                                {feedback.AssignToDetails.Name != null ? feedback.AssignToDetails.Name : 'Not assigned'}
                                                {feedback.AssignToDetails.EmployeeID != null ? ` (${feedback.AssignToDetails.EmployeeID})` : ''}
                                            </span>
                                        </TableCell>
                                    )}
                                    <TableCell className="creative-body-cell">
                                        <span className="cell-content process-content">{feedback.Process}</span>
                                    </TableCell>
                                    <TableCell className="creative-body-cell">
                                        <span className="cell-content issue-content">{feedback.IssueStatus}</span>
                                    </TableCell>
                                    <TableCell className="creative-body-cell">
                                        <Chip
                                            label={feedback.TicketStatus}
                                            className={`creative-status-chip ${getStatusClass(feedback.TicketStatus)}`}
                                            size="small"
                                        />
                                    </TableCell>
                                    <TableCell className="creative-body-cell creative-body-cell-last">
                                        <span className="cell-content date-content">{formatDate(feedback.UpdatedOn)}</span>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            ) : (
                <Box className="creative-no-records">
                    <div className="no-records-icon">📋</div>
                    <Typography variant="h6" component="div" className="no-records-title">
                        No record found
                    </Typography>
                    <Typography variant="body2" className="no-records-subtitle">
                        Try adjusting your search criteria or create a new record.
                    </Typography>
                </Box>
            )}
        </Box>
    );
};

export default FeedbackTable;