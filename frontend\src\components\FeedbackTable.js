import React from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Chip,
    Box,
    Typography,
    styled
} from '@mui/material';
import { formatDate } from '../services/CommonHelper';
import { Link } from 'react-router-dom';

// Styled components for modern table design
const ModernTableContainer = styled(TableContainer)(({ theme }) => ({
    borderRadius: '1.5rem',
    overflow: 'hidden',
    boxShadow: '0 10px 40px rgba(0, 0, 0, 0.08)',
    border: '1px solid #e2e8f0',
    position: 'relative',
    background: '#ffffff',

    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '4px',
        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #3b82f6 100%)',
        zIndex: 1,
    }
}));

const ModernTable = styled(Table)(({ theme }) => ({
    '& .MuiTableHead-root': {
        background: 'linear-gradient(135deg, #2d3748 0%, #4a5568 100%)',
        position: 'relative',

        '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #3b82f6 100%)',
        }
    },

    '& .MuiTableCell-head': {
        color: '#ffffff',
        fontWeight: 600,
        fontSize: '0.875rem',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        padding: '1.25rem 1rem',
        border: 'none',
        position: 'relative',

        '&:first-of-type': {
            paddingLeft: '1.5rem',
        },

        '&:last-of-type': {
            paddingRight: '1.5rem',
        },

        '&:not(:last-of-type)::after': {
            content: '""',
            position: 'absolute',
            right: 0,
            top: '25%',
            bottom: '25%',
            width: '1px',
            background: 'rgba(255, 255, 255, 0.2)',
        }
    },

    '& .MuiTableBody-root .MuiTableRow-root': {
        transition: 'all 0.3s ease',
        border: 'none',
        background: '#ffffff',

        '&:nth-of-type(even)': {
            background: '#f8fafc',
        },

        '&:hover': {
            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%)',
            transform: 'translateY(-1px)',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',

            '& .MuiTableCell-body': {
                borderColor: 'rgba(102, 126, 234, 0.2)',
            }
        },

        '&:last-of-type .MuiTableCell-body': {
            borderBottom: 'none',
        }
    },

    '& .MuiTableCell-body': {
        padding: '1rem',
        border: 'none',
        borderBottom: '1px solid #e2e8f0',
        fontSize: '0.875rem',
        color: '#1e293b',
        verticalAlign: 'middle',
        transition: 'all 0.3s ease',

        '&:first-of-type': {
            paddingLeft: '1.5rem',
            fontWeight: 600,
            color: '#667eea',
        },

        '&:last-of-type': {
            paddingRight: '1.5rem',
        }
    }
}));

const FeedbackLink = styled(Link)(({ theme }) => ({
    color: '#667eea',
    textDecoration: 'none',
    fontWeight: 600,
    padding: '0.5rem 1rem',
    borderRadius: '0.5rem',
    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%)',
    border: '1px solid rgba(102, 126, 234, 0.2)',
    display: 'inline-block',
    transition: 'all 0.3s ease',
    position: 'relative',
    overflow: 'hidden',

    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: '-100%',
        width: '100%',
        height: '100%',
        background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
        transition: 'left 0.5s',
    },

    '&:hover': {
        color: '#ffffff',
        background: 'linear-gradient(135deg, #667eea 0%, #3b82f6 100%)',
        borderColor: '#667eea',
        transform: 'translateY(-2px)',
        boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
        textDecoration: 'none',

        '&::before': {
            left: '100%',
        }
    },

    '&:focus': {
        outline: 'none',
        boxShadow: '0 0 0 3px rgba(102, 126, 234, 0.3)',
    }
}));

const StatusChip = styled(Chip)(({ status }) => {
    const getStatusColors = (status) => {
        const statusLower = status?.toLowerCase() || '';
        if (statusLower.includes('new')) return { bg: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)', color: '#ffffff' };
        if (statusLower.includes('open')) return { bg: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)', color: '#ffffff' };
        if (statusLower.includes('resolved')) return { bg: 'linear-gradient(135deg, #10b981 0%, #059669 100%)', color: '#ffffff' };
        if (statusLower.includes('closed')) return { bg: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)', color: '#ffffff' };
        if (statusLower.includes('pending')) return { bg: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)', color: '#ffffff' };
        return { bg: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)', color: '#ffffff' };
    };

    const colors = getStatusColors(status);

    return {
        background: colors.bg,
        color: colors.color,
        fontWeight: 600,
        fontSize: '0.75rem',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
        borderRadius: '1rem',
        padding: '0.375rem 0.75rem',
        border: 'none',

        '& .MuiChip-label': {
            padding: '0 0.5rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',

            '&::before': {
                content: '""',
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: '#ffffff',
                display: 'inline-block',
            }
        }
    };
});

const NoRecordsBox = styled(Box)(({ theme }) => ({
    textAlign: 'center',
    padding: '4rem 2rem',
    color: '#64748b',
    fontSize: '1.125rem',
    fontWeight: 500,
    background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
    borderRadius: '1rem',
    margin: '2rem',
    border: '2px dashed #e2e8f0',
    position: 'relative',

    '&::before': {
        content: '"📋"',
        fontSize: '3rem',
        display: 'block',
        marginBottom: '1rem',
        opacity: 0.5,
    }
}));

const FeedbackTable = ({ feedbacks, type = 0, redirectPage }) => {
    return (
        <Box sx={{ width: '100%' }}>
            {feedbacks.length > 0 ? (
                <ModernTableContainer component={Paper}>
                    <ModernTable>
                        <TableHead>
                            <TableRow>
                                <TableCell>FeedbackId</TableCell>
                                {[5].includes(type) && (
                                    <>
                                        <TableCell>Emp Name</TableCell>
                                        <TableCell>Emp ID</TableCell>
                                    </>
                                )}
                                <TableCell>Created On</TableCell>
                                {[2,3,5].includes(type) && (
                                    <>
                                        <TableCell>Matrix Role</TableCell>
                                        <TableCell>BU</TableCell>
                                    </>
                                )}
                                {[3,4,5].includes(type) && (
                                    <TableCell>AssignTo</TableCell>
                                )}
                                <TableCell>Process</TableCell>
                                <TableCell>SubProcess</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell>Updated On</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {feedbacks.map((feedback) => (
                                <TableRow key={feedback.TicketID}>
                                    <TableCell>
                                        <FeedbackLink to={`${redirectPage}${feedback.TicketID}`}>
                                            {feedback.TicketDisplayID}
                                        </FeedbackLink>
                                    </TableCell>
                                    {[5].includes(type) && (
                                        <>
                                            <TableCell>{feedback.CreatedByDetails.Name != null ? feedback.CreatedByDetails.Name : ''}</TableCell>
                                            <TableCell>{feedback.CreatedByDetails.EmployeeID != null ? feedback.CreatedByDetails.EmployeeID : ''}</TableCell>
                                        </>
                                    )}
                                    <TableCell>{formatDate(feedback.CreatedOn)}</TableCell>
                                    {[2,3,5].includes(type) && (
                                        <>
                                            <TableCell>{feedback.MatrixRole}</TableCell>
                                            <TableCell>{feedback.BU}</TableCell>
                                        </>
                                    )}
                                    {[3,4,5].includes(type) && (
                                        <TableCell>
                                            {feedback.AssignToDetails.Name != null ? feedback.AssignToDetails.Name : 'Not assigned'}
                                            {feedback.AssignToDetails.EmployeeID != null ? '(' + feedback.AssignToDetails.EmployeeID + ')' : ''}
                                        </TableCell>
                                    )}
                                    <TableCell>{feedback.Process}</TableCell>
                                    <TableCell>{feedback.IssueStatus}</TableCell>
                                    <TableCell>
                                        <StatusChip
                                            label={feedback.TicketStatus}
                                            status={feedback.TicketStatus}
                                            size="small"
                                        />
                                    </TableCell>
                                    <TableCell>{formatDate(feedback.UpdatedOn)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </ModernTable>
                </ModernTableContainer>
            ) : (
                <NoRecordsBox>
                    <Typography variant="h6" component="div" sx={{ mb: 1 }}>
                        No record found
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                        Try adjusting your search criteria or create a new record.
                    </Typography>
                </NoRecordsBox>
            )}
        </Box>
    );
};

export default FeedbackTable;