import React from 'react';
// import '../styles/FeedbackTable.css';
import { formatDate } from '../services/CommonHelper';
import { Link } from 'react-router-dom';

const FeedbackTable = ({ feedbacks, type = 0, redirectPage }) => {

    // Helper function to get status badge class
    const getStatusBadgeClass = (status) => {
        const statusLower = status?.toLowerCase() || '';
        if (statusLower.includes('new')) return 'status-new';
        if (statusLower.includes('open')) return 'status-open';
        if (statusLower.includes('resolved')) return 'status-resolved';
        if (statusLower.includes('closed')) return 'status-closed';
        if (statusLower.includes('pending')) return 'status-pending';
        return 'status-new'; // default
    };

    return (
        <div className="row clearfix">
            <div className="col-md-12">
                <div className="modern-table-container">
                    <div className="table-responsive">
                        {feedbacks.length > 0 ?
                            <table className="table table-hover js-basic-example dataTable table-custom">
                                    <thead className="thead-dark">
                                        <tr>
                                            <th>FeedbackId</th>
                                            {[5].includes(type) && <>
                                                <th>Emp Name</th>
                                                <th>Emp ID</th>
                                            </>}
                                            <th>Created On</th>
                                            {[2,3,5].includes(type) && <>  
                                                <th>Matrix Role</th>
                                                <th>BU</th>
                                            </>}
                                            {[3,4,5].includes(type) && 
                                                <th>AssignTo</th>
                                            }
                                            <th>Process</th>
                                            <th>SubProcess</th>
                                            <th>Status</th>
                                            <th>Updated On</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {feedbacks.map((feedback) => (
                                            <tr key={feedback.TicketID}>
                                                <td>
                                                    <Link to={`${redirectPage}${feedback.TicketID}`} className="feedback-link">
                                                        {feedback.TicketDisplayID}
                                                    </Link>
                                                </td>
                                                {[5].includes(type) && <>
                                                    <td>{feedback.CreatedByDetails.Name != null ? feedback.CreatedByDetails.Name : '' }</td>
                                                    <td>{feedback.CreatedByDetails.EmployeeID != null ? feedback.CreatedByDetails.EmployeeID : '' }</td>
                                                </>}
                                                <td>{formatDate(feedback.CreatedOn)}</td>
                                                {[2,3,5].includes(type) && <>
                                                    <td>{feedback.MatrixRole}</td>
                                                    <td>{feedback.BU}</td>
                                                </>}
                                                {[3,4,5].includes(type) && 
                                                    <td>{feedback.AssignToDetails.Name != null ? feedback.AssignToDetails.Name : 'Not assigned' }{feedback.AssignToDetails.EmployeeID != null ? '(' + feedback.AssignToDetails.EmployeeID + ')' : ''}</td>
                                                }
                                                <td>{feedback.Process}</td>
                                                <td>{feedback.IssueStatus}</td>
                                                <td>
                                                    <span className={`status-badge ${getStatusBadgeClass(feedback.TicketStatus)}`}>
                                                        {feedback.TicketStatus}
                                                    </span>
                                                </td>
                                                <td>{formatDate(feedback.UpdatedOn)}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                :
                                <div className="no-records">No record found</div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
    );
};

export default FeedbackTable; 