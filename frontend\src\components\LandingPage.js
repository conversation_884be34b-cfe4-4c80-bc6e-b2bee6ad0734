import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { GetSalesTicketUserDetails } from '../services/feedbackService';

const LandingPage = () => {
    const { type, ticketId } = useParams();
    const navigate = useNavigate();

    useEffect(() => {
        const handleUserDetails = (data) => {
            if (data.error) {
                navigate('/login');
                return false;
            }
            else if (!data || data.length === 0 || !data.EMPData) {
                navigate('/login');
                return false;
            }

            localStorage.setItem('UserDetails',
                JSON.stringify({
                    "EMPData": data.EMPData,
                    "Token": '',
                    "IsLocSet": 1,
                    "Location": data.Location,
                    "Issue": { "IssueID": 0, "SubIssueID": 0 }
                })
            );

            const userDetails = JSON.parse(localStorage.getItem('UserDetails')) || {};
            const BU = userDetails?.EMPData[0]?.BU ?? 0;

            if (BU === 0) {
                navigate('/editprofile');
            }
            else if (["ticket","jag","incentive"].includes(type)) {
                navigate('/createFeedBack');
            }
            else if (type === "notification") {
                navigate(`/MyTicketDetails/${ticketId}`);
            }
            else if (type === "ticketview") {
                navigate(`/TicketDetails/${ticketId}`);
            }
            else if (type === "MyFeedBack") {
                navigate(`/MyFeedBackDetails/${ticketId}`);
            }
            else {
                navigate('/myFeedBack');
            }

            return true;
        };

        GetSalesTicketUserDetails()
        .then((response) => {
            handleUserDetails(response);
        })
        .catch(() => {
            navigate('/login');
        })
    }, [type, ticketId, navigate]);


    return (
        <div className="d-flex flex-column align-items-center justify-content-center" style={{ minHeight: '100vh' }}>
            <div className="text-center">
                <div className="spinner-border text-primary mb-3" role="status">
                    <span className="sr-only">Loading...</span>
                </div>
                <h6 className="text-muted">
                    Please wait...
                </h6>
            </div>
        </div>
    );
};

export default LandingPage;