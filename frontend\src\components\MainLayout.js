import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import MyFeedback from './MyFeedback';
import CreateFeedback from './CreateFeedback';
import MyTicketDetails from './MyTicketDetails';
import MyAssignedTickets from './MyAssignedTickets';
import MySpanTickets from './MySpanTickets';
import MySpanCreatedTicket from './MySpanCreatedTicket';
import AllTickets from './AllTickets';
import TicketDetails from './TicketDetails';
import EditProfile from './EditProfile';
import PersonIcon from '@mui/icons-material/Person';
import WorkIcon from '@mui/icons-material/Work';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MailIcon from '@mui/icons-material/Mail';
import MarkEmailReadIcon from '@mui/icons-material/MarkEmailRead';

const MainLayout = () => {
    const location = useLocation();
    const [activeTab, setActiveTab] = useState(1);

    const getActiveTabFromRoute = (pathname) => {
        const tab2Routes = [
            '/AssignedFeedBack',
            '/MySpanFeedBack',
            '/MySpanCreatedFeedback',
            '/AllFeedBack',
            '/TicketDetails'
        ];

        const isTab2Route = tab2Routes.some(route =>
            pathname === route || pathname.startsWith(route + '/')
        );

        return isTab2Route ? 2 : 1;
    };

    useEffect(() => {
        const newActiveTab = getActiveTabFromRoute(location.pathname);
        setActiveTab(newActiveTab);
    }, [location.pathname]);

    const userDetails = JSON.parse(localStorage.getItem('UserDetails') || '{}');

    if (!userDetails || !userDetails.EMPData || !userDetails.EMPData[0]) {
        return <div>Loading...</div>;
    }

    const roleId = userDetails.EMPData[0].RoleID;
    const userName = userDetails.EMPData[0].Name;
    const userInitials = userName.split(' ').map(n => n[0]).join('').toUpperCase();

    return (
        <div className="modern-layout grid-layout">
            <aside className="modern-sidebar">
                <div className="modern-user-card">
                    <div className="modern-avatar">{userInitials}</div>
                    <div className="modern-username">{userName}</div>
                    <div className="modern-welcome">Welcome</div>
                </div>
                <div className="modern-nav-tabs">
                    <Link
                        className={`modern-nav-link${activeTab === 1 ? ' active' : ''}`}
                        to="/myFeedBack"
                        onClick={() => setActiveTab(1)}
                    >
                        <PersonIcon /> My FeedBack
                    </Link>
                    {[0, 13].indexOf(roleId) === -1 && (
                        <Link
                            className={`modern-nav-link${activeTab === 2 ? ' active' : ''}`}
                            to="/AssignedFeedBack"
                            onClick={() => setActiveTab(2)}
                        >
                            <WorkIcon style={{ marginRight: 8 }} /> My Account
                        </Link>
                    )}
                </div>
                <div className="modern-menu-section">
                    {activeTab === 1 && (
                        <nav>
                            <Link to="/createFeedBack" className="modern-menu-item">
                                <AddCircleIcon style={{ marginRight: 8 }} />
                                <span>Create FeedBack</span>
                            </Link>
                            <Link to="/myFeedBack" className="modern-menu-item">
                                <ConfirmationNumberIcon style={{ marginRight: 8 }} />
                                <span>My FeedBack</span>
                            </Link>
                        </nav>
                    )}
                    {activeTab === 2 && roleId !== 4 && (
                        <nav>
                            <Link to="/AssignedFeedBack" className="modern-menu-item">
                                <CheckCircleIcon style={{ marginRight: 8 }} />
                                <span>Assigned FeedBack</span>
                            </Link>
                            <Link to="/MySpanFeedBack" className="modern-menu-item">
                                <MailIcon style={{ marginRight: 8 }} />
                                <span>My Process Feedback</span>
                            </Link>
                            <Link to="/MySpanCreatedFeedback" className="modern-menu-item">
                                <MarkEmailReadIcon style={{ marginRight: 8 }} />
                                <span>My Span Feedback</span>
                            </Link>
                            <Link to="/AllFeedBack" className="modern-menu-item">
                                <ConfirmationNumberIcon style={{ marginRight: 8 }} />
                                <span>All FeedBack</span>
                            </Link>
                        </nav>
                    )}
                </div>
            </aside>
            <main className="modern-main-content">
                <Routes>
                    <Route path="/editprofile" element={<EditProfile />} />
                    <Route path="/myFeedBack" element={<MyFeedback />} />
                    <Route path="/createFeedBack" element={<CreateFeedback />} />
                    <Route path="/MyFeedbackDetails/:ticketId" element={<MyTicketDetails />} />
                    <Route path="/MyTicketDetails/:ticketId" element={<MyTicketDetails />} />
                    <Route path="/AssignedFeedBack" element={<MyAssignedTickets />} />
                    <Route path="/MySpanFeedBack" element={<MySpanTickets />} />
                    <Route path="/MySpanCreatedFeedback" element={<MySpanCreatedTicket />} />
                    <Route path="/AllFeedBack" element={<AllTickets />} />
                    <Route path="/TicketDetails/:ticketId" element={<TicketDetails />} />
                    <Route path="*" element={<MyFeedback />} />
                </Routes>
            </main>
        </div>
    );
};

export default MainLayout;

