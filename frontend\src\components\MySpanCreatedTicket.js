import React, { useState, useEffect } from 'react';
import {
    Box,
    Container,
    Fade
} from '@mui/material';
import {
    AccountTree as AccountTreeIcon
} from '@mui/icons-material';

// Common Components
import TicketPageHeader from './common/TicketPageHeader';
import DashboardStats from './common/DashboardStats';
import DataTableCard from './common/DataTableCard';

import { GetProcessMasterByAPI, GetAllIssueSubIssue, getStatusMaster, GetSpanCreatedTickets } from '../services/feedbackService';
// import DatePicker from 'react-datepicker';
// import "react-datepicker/dist/react-datepicker.css";
import '../styles/main.scss';
import alasql from 'alasql';
import * as XLSX from 'xlsx';
import { convertDotNetDate, formatDate } from '../services/CommonHelper';

const MySpanCreatedTicket = () => {
    const [stats, setStats] = useState({
        NEWCASE: 0,
        OPENCASE: 0,
        TATCASE: 0,
        Resolved: 0,
        Closed: 0
    });

    const [feedbacks, setFeedbacks] = useState([]);
    const [source, setSource] = useState([]);
    const [issueSubIssue, setIssueSubIssue] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [activeSearchType, setActiveSearchType] = useState(2);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [ticketId, setTicketId] = useState('');
    const [spanTicket, setSpanTicket] = useState({});
    const [selected, setSelected] = useState({
        Source: { SourceID: 0, Name: 'Select' },
        IssueType: undefined,
        Status: undefined,
        Product: { ProductID: 0, Name: 'Select' }
    });

    const userDetails = JSON.parse(window.localStorage.getItem('UserDetails'));

    const ProductOptions = [
        { 'ProductID': 0, 'Name': 'Select' },
        { 'ProductID': 115, 'Name': 'Investment' },
        { 'ProductID': 7, 'Name': 'Term' },
        { 'ProductID': 2, 'Name': 'Health' },
        { 'ProductID': 117, 'Name': 'Motor' }
    ];

    useEffect(() => {
        GetAllProcess();
        GetDashboardCount(3);
        getAllStatusMaster();
        getAllIssueSubIssueService();
    }, []);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
            .then((data) => {
                if (data && data.length > 0) {
                    data.unshift({ Name: "Select", SourceID: 0 });
                    setSource(data);
                    if (userDetails?.EMPData[0]?.ProcessID > 0) {
                        const userProcess = data.find(item => item.SourceID === userDetails.EMPData[0].ProcessID);
                        setSelected(prev => ({
                            ...prev,
                            Source: userProcess || { SourceID: userDetails.EMPData[0].ProcessID, Name: 'Unknown Process' }
                        }));
                    }
                }
            })
            .catch(() => {
                setSource([]);
            });
    };

    const GetDashboardCount = (_type) => {
        const objRequest = {
            type: _type,
        };

        GetSpanCreatedTickets(objRequest)
            .then((data) => {
                if (data.length > 0) {
                    setSpanTicket(data);
                    const CategoryCounts = Object.entries(data).map(([category, data]) => ({
                        category: data.Key,
                        ticketCount: data.Value.Count
                    }));
                    if (CategoryCounts && Array.isArray(CategoryCounts) && CategoryCounts.length > 0) {
                        CategoryCounts.forEach(item => {
                            switch (item.category) {
                                case 1:
                                    setStats(prev => ({ ...prev, NEWCASE: item.Count }));
                                    break;
                                case 2:
                                    setStats(prev => ({ ...prev, OPENCASE: item.Count }));
                                    break;
                                case 3:
                                    setStats(prev => ({ ...prev, Resolved: item.Count }));
                                    break;
                                case 4:
                                    setStats(prev => ({ ...prev, Closed: item.Count }));
                                    break;
                                case 5:
                                    setStats(prev => ({ ...prev, TATCASE: item.Count }));
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                } else {
                    setSpanTicket({});
                    setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
                }
            })
            .catch(() => {
                setSpanTicket({});
                setStats({ NEWCASE: 0, OPENCASE: 0, TATCASE: 0, Resolved: 0, Closed: 0 });
            });
    };

    const getAllIssueSubIssueService = () => {
        GetAllIssueSubIssue()
            .then((data) => {
                if (data && data.length > 0) {
                    setIssueSubIssue(data);
                }
            })
            .catch(() => {
                setIssueSubIssue([]);
            });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
            .then((data) => {
                if (data && data.length > 0) {
                    setStatusList(data);
                }
            })
            .catch(() => {
                setStatusList([]);
            });
    };

    const GetAgentTicketList = (status) => {
        const statusId = status !== 8 ? status : selected.Status?.StatusID || 0;

        var FromDate = formatDateForRequest(fromDate,3);
        var ToDate = formatDateForRequest(toDate,0);

        if(status === 8){
            FromDate = formatDateForRequest(fromDate,0);
            ToDate = formatDateForRequest(toDate,0);
        } 

        FromDate = new Date(FromDate);
        ToDate = new Date(ToDate);

        if (spanTicket != null && spanTicket != {}) {
            var FilteredData = spanTicket;
            var flatdata = Object.values(FilteredData).flatMap(group => group.Value.Tickets);
            if (flatdata && Array.isArray(flatdata) && flatdata.length > 0)
            {
                FilteredData = Array.from(
                    new Map(flatdata.map(item => [item.TicketDisplayID, item])).values()
                );

                //filter based on fromdate to date
                FilteredData = FilteredData.filter(ticket => {
                    const createdOn = new Date(convertDotNetDate(ticket.CreatedOn));
                    return createdOn >= FromDate && createdOn <= ToDate;
                });

                //Selected Status
                if (statusId > 0) {
                    FilteredData = spanTicket[(statusId - 1).toString()]?.Value?.Tickets || [];
                }

                //Selected Process
                if (selected && selected.Source && selected.Source.SourceID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        const ProcessName = selected.Source.Name;
                        return ProcessName == ticket.Process;
                    });
                }

                //Selected Sub-Process
                if (selected && selected.IssueType && selected.IssueType.IssueID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        const IssuName = selected.IssueType.ISSUENAME;
                        return IssuName == ticket.IssueStatus;
                    });
                }

                //Selected ProductID
                if (selected && selected.Product && selected.Product.ProductID > 0) {
                    FilteredData = FilteredData.filter(ticket => {
                        return selected.Product.ProductID == ticket.ProductId;
                    });
                }
                //Selected TicketID
                if (ticketId != undefined && ticketId.trim() != '') {
                    FilteredData = FilteredData.filter(ticket => {
                        return ticketId.trim().toUpperCase() == ticket.TicketDisplayID.toUpperCase();
                    });
                }
            }
            setFeedbacks(FilteredData);
        }

    };

    const formatDateForRequest = (date, yearDuration = 0) => {
        const d = new Date(date);
        const year = d.getFullYear() - yearDuration;
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const exportData = () => {
        if (typeof window !== 'undefined') {
            window.XLSX = XLSX;
        }

        alasql.fn.datetime = function (dateStr) {
            if (!dateStr) return '';
            
            return formatDate(dateStr);
        };
        
        alasql(
            'SELECT TicketDisplayID AS TicketID,datetime(CreatedOn) AS CreatedOn,CreatedByUserName as Name,'
            + 'CreatedByEmployeeId as EmpID,'
            + 'AssignToUserName as AssignTo,AssignToEmployeeID as AssignToEcode,'
            + 'Process,IssueStatus,TicketStatus,datetime(UpdatedOn) UpdatedOn'
            + ' INTO XLSX("Data_' + new Date().toDateString() + '.xlsx", { headers: true }) FROM ? ',
            [feedbacks]
        );
    };

    const resetFilters = () => {
        setSelected({
            Source: { SourceID: 0, Name: 'Select' },
            IssueType: undefined,
            Status: undefined,
            Product: { ProductID: 0, Name: 'Select' }
        });
        setTicketId('');
        setFromDate(new Date());
        setToDate(new Date());
    };

    const statCards = [
        { label: 'New', count: stats.NEWCASE || 0, id: 1, color: '#4facfe', className: 'new-status' },
        { label: 'Open', count: stats.OPENCASE || 0, id: 2, color: '#fcb69f', className: 'open-status' },
        { label: 'TAT Bust', count: stats.TATCASE || 0, id: 5, color: '#ff9a9e', className: 'tat-status' },
        { label: 'Resolved', count: stats.Resolved || 0, id: 3, color: '#a8edea', className: 'resolved-status' },
        { label: 'Closed', count: stats.Closed || 0, id: 4, color: '#667eea', className: 'closed-status' }
    ];

    return (
        <Box className="assigned-tickets-main">
            <Container maxWidth="xl" className="assigned-tickets-container">
                <Fade in timeout={800}>
                    <Box>
                        {/* Header and Search Form */}
                        <TicketPageHeader
                            title="My Span Created Tickets"
                            subtitle="Manage tickets created under your span of control"
                            icon={AccountTreeIcon}
                            activeSearchType={activeSearchType}
                            setActiveSearchType={setActiveSearchType}
                            resetFilters={resetFilters}
                            fromDate={fromDate}
                            setFromDate={setFromDate}
                            toDate={toDate}
                            setToDate={setToDate}
                            selected={selected}
                            setSelected={setSelected}
                            ticketId={ticketId}
                            setTicketId={setTicketId}
                            source={source}
                            issueSubIssue={issueSubIssue}
                            statusList={statusList}
                            ProductOptions={ProductOptions}
                            onSearch={() => GetAgentTicketList(8)}
                            showProductField={true}
                            searchButtonText="Search Tickets"
                        />

                        {/* Dashboard Stats */}
                        {activeSearchType === 2 && (
                            <DashboardStats
                                statCards={statCards}
                                onStatClick={GetAgentTicketList}
                            />
                        )}

                        {/* Data Table */}
                        <DataTableCard
                            feedbacks={feedbacks}
                            onExport={exportData}
                            tableType={4}
                            redirectPage="/TicketDetails/"
                            tableTitle="Span Created Ticket Results"
                        />
                    </Box>
                </Fade>
            </Container>
        </Box>
    );
};

export default MySpanCreatedTicket;