import React from 'react';
import {
    Box,
    Container,
    Typography,
    Paper,
    Grid,
    Chip
} from '@mui/material';
import FeedbackTable from './FeedbackTable';
import DataTableCard from './common/DataTableCard';

// Demo data for showcasing the modern table design
const demoFeedbacks = [
    {
        TicketID: 1,
        TicketDisplayID: 'FB-2024-001',
        CreatedOn: '2024-01-15T10:30:00Z',
        Process: 'Customer Support',
        IssueStatus: 'Payment Issue',
        TicketStatus: 'New',
        UpdatedOn: '2024-01-15T10:30:00Z',
        CreatedByDetails: {
            Name: '<PERSON>',
            EmployeeID: 'EMP001'
        },
        AssignToDetails: {
            Name: '<PERSON>',
            EmployeeID: 'EMP002'
        },
        MatrixRole: 'Senior Agent',
        BU: 'Sales'
    },
    {
        TicketID: 2,
        TicketDisplayID: 'FB-2024-002',
        CreatedOn: '2024-01-14T14:20:00Z',
        Process: 'Technical Support',
        IssueStatus: 'System Error',
        TicketStatus: 'Open',
        UpdatedOn: '2024-01-15T09:15:00Z',
        CreatedByDetails: {
            Name: '<PERSON>',
            EmployeeID: 'EMP003'
        },
        AssignToDetails: {
            Name: 'Bob Wilson',
            EmployeeID: 'EMP004'
        },
        MatrixRole: 'Technical Lead',
        BU: 'IT'
    },
    {
        TicketID: 3,
        TicketDisplayID: 'FB-2024-003',
        CreatedOn: '2024-01-13T16:45:00Z',
        Process: 'Billing Support',
        IssueStatus: 'Invoice Query',
        TicketStatus: 'Resolved',
        UpdatedOn: '2024-01-14T11:30:00Z',
        CreatedByDetails: {
            Name: 'Mike Brown',
            EmployeeID: 'EMP005'
        },
        AssignToDetails: {
            Name: 'Sarah Davis',
            EmployeeID: 'EMP006'
        },
        MatrixRole: 'Billing Specialist',
        BU: 'Finance'
    },
    {
        TicketID: 4,
        TicketDisplayID: 'FB-2024-004',
        CreatedOn: '2024-01-12T11:15:00Z',
        Process: 'Product Support',
        IssueStatus: 'Feature Request',
        TicketStatus: 'Closed',
        UpdatedOn: '2024-01-13T15:20:00Z',
        CreatedByDetails: {
            Name: 'Emma Wilson',
            EmployeeID: 'EMP007'
        },
        AssignToDetails: {
            Name: 'David Lee',
            EmployeeID: 'EMP008'
        },
        MatrixRole: 'Product Manager',
        BU: 'Product'
    },
    {
        TicketID: 5,
        TicketDisplayID: 'FB-2024-005',
        CreatedOn: '2024-01-11T13:30:00Z',
        Process: 'General Inquiry',
        IssueStatus: 'Information Request',
        TicketStatus: 'Pending',
        UpdatedOn: '2024-01-12T10:45:00Z',
        CreatedByDetails: {
            Name: 'Chris Taylor',
            EmployeeID: 'EMP009'
        },
        AssignToDetails: {
            Name: null,
            EmployeeID: null
        },
        MatrixRole: 'Customer Service',
        BU: 'Support'
    }
];

const TableDemo = () => {
    const handleExport = () => {
        console.log('Export functionality would be implemented here');
    };

    return (
        <Container maxWidth="xl" sx={{ py: 4 }}>
            {/* Header Section */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h3" component="h1" gutterBottom sx={{ 
                    fontWeight: 700, 
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    textAlign: 'center'
                }}>
                    Modern Table Design Demo
                </Typography>
                <Typography variant="h6" sx={{ 
                    textAlign: 'center', 
                    color: '#64748b',
                    mb: 3
                }}>
                    Creative MUI-based table with class architecture and preserved functionality
                </Typography>
            </Box>

            {/* Features Grid */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                        <Chip label="Responsive Design" color="primary" sx={{ mb: 1 }} />
                        <Typography variant="body2">Mobile-first responsive table</Typography>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                        <Chip label="Status Badges" color="secondary" sx={{ mb: 1 }} />
                        <Typography variant="body2">Color-coded status indicators</Typography>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                        <Chip label="Hover Effects" color="success" sx={{ mb: 1 }} />
                        <Typography variant="body2">Smooth animations and transitions</Typography>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
                        <Chip label="Modern Styling" color="warning" sx={{ mb: 1 }} />
                        <Typography variant="body2">Gradient headers and shadows</Typography>
                    </Paper>
                </Grid>
            </Grid>

            {/* Demo Tables */}
            <Grid container spacing={4}>
                {/* Basic Table (type 0) */}
                <Grid item xs={12}>
                    <DataTableCard
                        feedbacks={demoFeedbacks}
                        onExport={handleExport}
                        tableType={0}
                        redirectPage="/ticket-details/"
                        tableTitle="Basic Feedback Table (Type 0)"
                        showExport={true}
                    />
                </Grid>

                {/* Extended Table (type 5) */}
                <Grid item xs={12}>
                    <DataTableCard
                        feedbacks={demoFeedbacks}
                        onExport={handleExport}
                        tableType={5}
                        redirectPage="/ticket-details/"
                        tableTitle="Extended Feedback Table (Type 5) - All Columns"
                        showExport={true}
                    />
                </Grid>

                {/* Admin Table (type 3) */}
                <Grid item xs={12}>
                    <DataTableCard
                        feedbacks={demoFeedbacks}
                        onExport={handleExport}
                        tableType={3}
                        redirectPage="/ticket-details/"
                        tableTitle="Admin Feedback Table (Type 3) - With Assignment"
                        showExport={true}
                    />
                </Grid>
            </Grid>

            {/* Features Description */}
            <Box sx={{ mt: 6, p: 4, bgcolor: '#f8fafc', borderRadius: 3 }}>
                <Typography variant="h5" gutterBottom sx={{ fontWeight: 600, color: '#1e293b' }}>
                    Key Features Implemented:
                </Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            ✨ <strong>Modern MUI Components:</strong> Using Table, TableContainer, TableHead, TableBody, TableRow, TableCell
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            🎨 <strong>Styled Components:</strong> Custom styled components with theme integration
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            🏷️ <strong>Status Badges:</strong> Color-coded chips with gradients for different statuses
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            🔗 <strong>Enhanced Links:</strong> Interactive feedback links with hover animations
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            📱 <strong>Responsive Design:</strong> Mobile-optimized with horizontal scroll
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 1 }}>
                            🎭 <strong>Preserved Functionality:</strong> All original features maintained
                        </Typography>
                    </Grid>
                </Grid>
            </Box>
        </Container>
    );
};

export default TableDemo;
