/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { GetTicketDetails, UpdateTicketRemarks, UploadFile, GetProcessMasterByAPI, getStatusMaster, GetSalesTicketProcessUser, AssignSalesTicket, ReAssignSalesTicket, GetSalesTicketLog, GetDocumentUrl } from '../services/feedbackService';
import { formatDate } from '../services/CommonHelper';

const TicketDetails = () => {
    const { ticketId } = useParams();
    const [ticketDetails, setTicketDetails] = useState([]);
    const [commentList, setCommentList] = useState([]);
    const [ticketReply, setTicketReply] = useState('');
    const [hrComments, setHrComments] = useState('');
    const [fileAttachments, setFileAttachments] = useState([]);
    const [IsShowReassignFlag, setIsShowReassignFlag ] = useState(1);
    const [inActive, setInActive] = useState(false);
    const [selected, setSelected] = useState({
        Status: undefined,
        IssueType: undefined,
        SubIssueType: undefined,
        Source: { SourceID: 0 },
        Spoc: undefined
    });
    const [sourceList, setSourceList] = useState([]);
    const [spocList, setSpocList] = useState([]);
    const [statusList, setStatusList] = useState([]);
    const [ticketLog, setTicketLog] = useState([]);
    const [activeTab, setActiveTab] = useState(1);
    const [updateAssignmentFlag, setUpdateAssignmentFlag] = useState(0);
    const [isSupport, setIsSupport] = useState(0);
    const userDetails = JSON.parse(localStorage.getItem('UserDetails')) || {};

    useEffect(() => {
        GetAllProcess();
        getAllStatusMaster();
        getTicketDetailsService();
    }, [ticketId]);

    const GetAllProcess = () => {
        GetProcessMasterByAPI()
        .then((data) => {
            if (data && data.length > 0) {
                data.unshift({ Name: "Select", SourceID: 0 });
                setSourceList(data);
                setSelected({
                    IssueType: undefined,
                    SubIssueType: undefined,
                    Status: undefined,
                    Source: data[0]
                })
            } else {
                setSourceList([]);
            }
        })
        .catch(() => {
            setSourceList([]);
        });
    };

    const getAllStatusMaster = () => {
        getStatusMaster()
        .then((data) => {
            if (data && data.length > 0) {
                setStatusList(data);
            }
        })
        .catch(() => {
            setStatusList([]);
        });
    };

    const getTicketDetailsService = () => {
        const req = {
            "ticketId" : ticketId
        };
        GetTicketDetails(req)
        .then(data => {
            if(data)
            {
                setTicketDetails(data);
                setCommentList(data.Commentlist || []);
                if (data.processId == 11) {
                    setIsShowReassignFlag(0);
                }
                setSelected(prev => ({
                    ...prev,
                    Status: { StatusID: data.StatusID },
                    IssueType: { ISSUEID: data.IssueID },
                    Source: { SourceID: data.ProcessID }
                }));
                if (data.StatusID == 4 || data.StatusID == 6) {
                    setInActive(true);
                }
                getSalesTicketLogService();
            } else {
                setTicketDetails([]);
                setCommentList([]);
                setSelected({
                    Status: undefined,
                    IssueType: undefined,
                    SubIssueType: undefined,
                    Source: { SourceID: null }
                });
            }
        })
        .catch(() => {
            setTicketDetails([]);
            setCommentList([]);
            setSelected({
                Status: undefined,
                IssueType: undefined,
                SubIssueType: undefined,
                Source: { SourceID: null }
            });
        })
    };

    const getSalesTicketLogService = () => {
        const req = {
            ticketId,
            logtype: 0
        };

        GetSalesTicketLog(req)
        .then((data) => {
            if (data && data.length > 0) {
                const sortedFeedbacks = [...data].sort((a, b) => 
                    new Date(b.CreatedOn) - new Date(a.CreatedOn)
                );
                setTicketLog(sortedFeedbacks);
            } else {
                setTicketLog([]);
            }
        })
        .catch(() => {
            setTicketLog([]);
        });
    };

    const ReAssignSalesTicketService = () => {
        const requestData = {
            "TicketID": ticketId
        }

        ReAssignSalesTicket(requestData)
        .then((data) => {
            if(data.Status) 
            {
                getTicketDetailsService();
                setUpdateAssignmentFlag(0);
                toast.success("Updated successfully.");
            }
        })
        .catch(() => {

        })
    }

    const UpdateAssignment = (inType, selectedProcessId = 0) => {
        if(inType == 1)
        {
            const requestData = {
                ticketId: ticketId,
                ProcessId: ticketDetails.ProcessID
            }
            GetSalesTicketProcessUser(requestData)
            .then((result) => {
                setSpocList(result || []);
            })
            .catch(() => {
                setSpocList([]);
            })

            setSelected({
                IssueType: undefined, 
                SubIssueType: undefined, 
                Status: undefined,
                Source: { SourceID: ticketDetails.ProcessID }
            });
            setUpdateAssignmentFlag(1);
        } 
        else if (inType == 4)
        {
            const requestData = {
                ticketId: ticketId,
                ProcessId: selectedProcessId
            }
            GetSalesTicketProcessUser(requestData)
            .then((result) => {
                setSpocList(result || []);
            })
            .catch(() => {
                setSpocList([]);
            })
            setUpdateAssignmentFlag(1);
        }
        else if (inType == 2)
        {
            if(selected.Spoc && selected.Spoc != undefined && selected.Source != undefined)
            {
                const requestData = {
                    TicketID: ticketId,
                    ProcessId: selected.Source.SourceID,
                    AsssignToUserID: userDetails?.EMPData[0]?.EmpID ?? 0,
                    Type: 2
                }
                AssignSalesTicket(requestData)
                .then(() => {
                    toast.success("Assignment updated successfully");
                    setUpdateAssignmentFlag(0);
                    getTicketDetailsService();
                }); 
            } else {
                toast('Please select Process and spoc.', { type: 'error' });
                return false;
            }
        }
        else if(inType == 3)
        {
            setSelected({
                IssueType: undefined, 
                SubIssueType: undefined, 
                Status: undefined,
                Source: { SourceID: sourceList[0] }
            });
            setUpdateAssignmentFlag(0);
        }
    }

    const handleFileChange = (e) => {
        const files = Array.from(e.target.files);
        const readers = files.map(file => {
            return new Promise(resolve => {
                const reader = new FileReader();
                reader.onload = () => {
                    resolve({
                        FileName: file.name,
                        AttachemntContent: btoa(reader.result),
                        AttachmentURL: '',
                        ContentType: file.type
                    });
                };
                reader.readAsBinaryString(file);
            });
        });
        Promise.all(readers).then(data => setFileAttachments(data));
    };

    const updateRemarks = (ReplyType) => {
        var commentText = '';
        if(ReplyType == 2)
        {
            commentText = ticketReply;
            if(selected.Status?.StatusID == 1)
            {
                toast("Please change the status to In Progress", { type: 'error' });
                return false;
            }
        }
        else
        {
            commentText = hrComments;
        }

        if (!commentText || commentText.length <= 10) {
            toast.error("Remark should be more than 10 characters");
            return;
        }

        const requestData = {
            TicketID: ticketId,
            Comments: commentText,
            StatusID: selected.Status?.StatusID,
            CreatedBy: userDetails?.EMPData[0]?.EmpID ?? 0,
            ReplyType,
            FileURL: '',
            FileName: ''
        };

        if (fileAttachments.length > 0) {
            UploadFile(fileAttachments)
            .then(fileData => {
                requestData.FileURL = fileData[0].AttachmentURL;
                requestData.FileName = fileData[0].FileName;

                UpdateTicketRemarks(requestData)
                .then(() => {
                    toast.success('Updated successfully');
                    getTicketDetailsService();
                    setTicketReply('');
                    setFileAttachments([]);
                });
            });
        } else {
            UpdateTicketRemarks(requestData)
            .then(() => {
                toast.success('Updated successfully');
                getTicketDetailsService();
                setTicketReply('');
            });
        }
    };

    const getDocumentUrl = (docId, refId) => {
        const requestData = {
            "docId": docId,
            "RefId": refId
        }
        GetDocumentUrl(requestData)
            .then((response) => {
                const data = response;
                if (data?.ttlDocUrl) {
                    window.open(data.ttlDocUrl, '_blank');
                }
            })
            .catch(() => {

            })   
    };
    
    const openInNewTab = (url) => {
        if (url) {
            window.open(url, '_blank');
        }
    };

    return (
        <div className="container-fluid ticketdetails">
            <div className="block-header">
                <div className="row">
                    <div className="col-lg-6 col-md-8 col-lg-12">
                        <div className="detail_links">
                            <h2>
                                <button  type="button" className="btn btn-info" onClick={() => window.history.back()}>Back</button>
                            </h2>
                            <p className="demo-button">
                                <span className="assign_hd">Assigned To :</span>
                                {updateAssignmentFlag === 0 ? (
                                    <span className="tat_green">
                                        {ticketDetails?.AssignToDetails?.Name || 'Not assigned'}
                                        {ticketDetails?.AssignToDetails?.EmployeeID ? `(${ticketDetails.AssignToDetails.EmployeeID})` : ''}
                                    </span>
                                ) : (
                                    <>
                                        <select className="data_list" value={selected.Source?.SourceID || 0}
                                            onChange={(e) => {
                                                const sourceId = parseInt(e.target.value);
                                                const source = sourceList.find(s => s.SourceID === sourceId);
                                                setSelected(prev => ({ ...prev, Source: source }));
                                                UpdateAssignment(4, sourceId);
                                            }}>
                                            {sourceList.map((data, idx) => (
                                                <option key={idx} value={data.SourceID}>{data?.Name}</option>
                                            ))}
                                        </select>

                                        <select className="data_list" value={selected.Spoc?.EmployeeID || ''}
                                            onChange={(e) => {
                                                const spocId = e.target.value;
                                                const spoc = spocList.find(s => s.EmployeeID.toString() === spocId);
                                                setSelected(prev => ({ ...prev, Spoc: spoc }));
                                            }}>
                                            <option value="">Select Spoc</option>
                                            {spocList.map((data, idx) => (
                                                <option key={idx} value={data.EmployeeID}>{data.UserDisplayName}</option>
                                            ))}
                                        </select>
                                    </>
                                )}
                                {updateAssignmentFlag === 0 && IsShowReassignFlag === 1 && (
                                    userDetails.EMPData[0].Userlevel === 4 && ticketDetails.AssignToDetails?.EmpID === userDetails.EMPData[0].EmpID ? (
                                        <button 
                                            className="btn btn-outline-success" 
                                            onClick={() => ReAssignSalesTicketService()}
                                            disabled={inActive}
                                        >
                                            Re-assign
                                        </button>
                                    ) : (
                                        userDetails.EMPData[0].Userlevel !== 4 ?
                                            <button 
                                                className="btn btn-outline-success" 
                                                onClick={() => UpdateAssignment(1)}
                                                disabled={inActive}
                                            >
                                                Re-assign
                                            </button>
                                        : null
                                    )
                                )}

                                {updateAssignmentFlag === 1 && (
                                    <>
                                        <button className="btn btn-outline-success" onClick={() => UpdateAssignment(2)}>Update</button>

                                        <button className="btn btn-outline-success" onClick={() => UpdateAssignment(3)}>Cancel</button>
                                    </>
                                )}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="row clearfix">
                <div className="col-lg-12">
                    <div className="card">
                        <div className="mobile-left">
                            <a className="btn btn-primary toggle-email-nav collapsed" data-toggle="collapse" href="#email-nav" role="button" aria-expanded="false" aria-controls="email-nav">
                                <span className="btn-label">
                                    <i className="la la-bars"></i>
                                </span>
                                Menu
                            </a>
                        </div>
                        <div className="mail-inbox">
                            <div className="mail-left collapse filter_box" id="email-nav">
                                <div className="mail-side mail-sidenew">
                                    <ul className="nav">
                                        <li><label>Process</label>{ticketDetails?.Process}</li>
                                        <li><label>FeedBack</label>{ticketDetails?.IssueStatus}</li>
                                        <li><label>Status</label>{ticketDetails?.TicketStatus}</li>
                                        <li><label>Created By</label>{ticketDetails?.CreatedByDetails?.Name}({ticketDetails?.CreatedByDetails?.EmployeeID})</li>
                                        {ticketDetails?.AssignToDetails?.EmpID > 0 && (
                                            <li><label>Assigned to</label>{ticketDetails?.AssignToDetails?.Name}({ticketDetails?.AssignToDetails?.EmployeeID})</li>
                                        )}
                                        <li><label>User Role</label>{ticketDetails?.MatrixRole}</li>
                                        <li><label>BU</label>{ticketDetails?.BU}</li>
                                        {ticketDetails?.CreatedByDetails?.MatrixRoleId !== 13 && (
                                            <li><label>Mobile Number</label>{ticketDetails?.CreatedByDetails?.ContactNo}</li>
                                        )}
                                        {ticketDetails?.CreatedByDetails?.MatrixRoleId === 13 && (
                                            <><li><label>Reports To:</label>{ticketDetails?.ManagerDetails?.Name}</li>
                                            <li><label>ManagerNo.</label>&nbsp;{ticketDetails?.ManagerDetails?.ManagerNo}</li></>
                                        )}
                                    </ul>                        
                                </div>
                            </div>
                            <div className="mail-right">
                                <div className="body ticket_detailbox">
                                    <ul className="nav nav-tabs">
                                        <li className="nav-item"><a className={`nav-link ${activeTab === 1 ? 'active show' : ''}`} onClick={() => setActiveTab(1)}>FeedBack Detail</a></li>
                                        <li className="nav-item"><a className={`nav-link ${activeTab === 3 ? 'active show' : ''}`} onClick={() => setActiveTab(3)}>Log Details</a></li>
                                    </ul>
                                    <div className="tab-content table_databox">
                                        {activeTab === 1 && (
                                            <div className="tab-pane show active">
                                                <div className="table-responsive">
                                                    <table className="table m-b-0">
                                                        <thead>
                                                            <tr>
                                                                <th>Ticket Id</th>
                                                                <th>Created on</th>
                                                                <th>Process</th>
                                                                <th>FeedBack</th>
                                                                <th>Product</th>
                                                                <th>Status</th>
                                                                <th>Last Updated on</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr className="active_detaillist">
                                                                <td>{ticketDetails?.TicketDisplayID}</td>
                                                                <td>{ticketDetails.CreatedOn ? formatDate(ticketDetails.CreatedOn) : ''}</td>
                                                                <td>{ticketDetails?.Process}</td>
                                                                <td>{ticketDetails?.IssueStatus}</td>
                                                                <td>{ticketDetails?.ProductName}</td>
                                                                <td>{ticketDetails?.TicketStatus}</td>
                                                                <td>{ticketDetails.UpdatedOn ? formatDate(ticketDetails.UpdatedOn) : ''}</td>
                                                            </tr>
                                                            <tr>
                                                                <td colSpan={7} className="tkt_detailbox">
                                                                    <div className="card detialbox">
                                                                        <div className="body emailer_body">
                                                                            {commentList.filter(c => c.ReplyType === 1 || c.ReplyType === 2).map((c, idx) => (
                                                                                <div key={idx} className={`timeline-item detail_data ${c.ReplyType === 1 ? 'green' : 'blue'}`}>
                                                                                    <div style={{margin: "0 0 14px 0"}}>
                                                                                        <span className="date">From: <a>{c.User.UserName} ({c.User.EmployeeId})</a></span>
                                                                                        <div className="right_section">
                                                                                            <span className="sl-date">{formatDate(c.CreatedOn)}</span>
                                                                                        </div>
                                                                                        <div style={{ width:"100%", display:"block" }}>
                                                                                            <p>{c.Comment}</p>
                                                                                                {c.FileURL && c.FileURL !== -1 && c.FileURL.indexOf('https') === -1 && (
                                                                                                    <a
                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}
                                                                                                        onClick={() => getDocumentUrl(c.FileURL, c.RefId)}                                                                                                    >
                                                                                                        {c.FileName}
                                                                                                    </a>)
                                                                                                }
                                                                                                {c.FileURL && c.FileURL !== -1 && c.FileURL.indexOf('https') !== -1 && (
                                                                                                    <a
                                                                                                        style={{cursor: 'pointer', textDecoration: 'underline', color: '#007bff'}}
                                                                                                        onClick={() => openInNewTab(c.FileURL)}
                                                                                                    >
                                                                                                        {ticketDetails.RefTicketID}
                                                                                                    </a>)
                                                                                                }
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                        <div className="mail_compose_Section">
                                                                            <div className="card shadow_none">
                                                                                <div className="body compose_box">
                                                                                    <hr />
                                                                                    <div className="header sub_hd">
                                                                                        <h2>Respond to Query</h2>
                                                                                    </div>
                                                                                    <textarea 
                                                                                        value={ticketReply} 
                                                                                        onChange={(e) => setTicketReply(e.target.value)} 
                                                                                        cols="85" 
                                                                                        rows="10"
                                                                                        disabled={inActive}
                                                                                    ></textarea>
                                                                                    <div className="m-t-30 compose_action_button">
                                                                                        <div style={{ display:'block', width: "100%", padding: "0 0 10px 0" }}>
                                                                                            <label>Choose Status.</label>
                                                                                            <select className="form-control"
                                                                                                value={selected.Status?.StatusID || ''}
                                                                                                onChange={(e) => {
                                                                                                    const statusId = parseInt(e.target.value);
                                                                                                    const found = statusList.find(s => s.StatusID === statusId);
                                                                                                    setSelected(prev => ({ ...prev, Status: found }));
                                                                                                }}>
                                                                                                <option value="">Select Status</option>
                                                                                                {statusList.map((status, idx) => (
                                                                                                    <option key={idx} value={status.StatusID}>{status.StatusName}</option>
                                                                                                ))}
                                                                                            </select>
                                                                                        </div>
                                                                                        <div className="upload_box ng-scope">
                                                                                            <input 
                                                                                                type="file"
                                                                                                id="file-3"
                                                                                                className="inputfile inputfile-3" 
                                                                                                multiple 
                                                                                                onChange={handleFileChange} 
                                                                                                disabled={inActive}
                                                                                                accept=".jpg,.pdf,.xlsx"
                                                                                            />
                                                                                            <label htmlFor="file-3" className="upload_docs" style={{ display: 'inline' }}>
                                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17">
                                                                                                    <path d="M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.1.5.7.9 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z" />
                                                                                                </svg>
                                                                                            </label>
                                                                                            {fileAttachments.map((f, i) => (
                                                                                                <span key={i} className="attachment_files">{f.FileName} <em onClick={() => {
                                                                                                    const updated = [...fileAttachments];
                                                                                                    updated.splice(i, 1);
                                                                                                    setFileAttachments(updated);
                                                                                                }}>X</em></span>
                                                                                            ))}
                                                                                        </div>
                                                                                        <button 
                                                                                            className="btn btn-success" 
                                                                                            onClick={() => updateRemarks(2)}
                                                                                            disabled={inActive}
                                                                                            style={{ verticalAlign : "top", margin : "0 0 0 10px" }}
                                                                                        >
                                                                                            Post
                                                                                        </button>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        )}
                                        {activeTab === 2 && (<>
                                            <div className="tab-pane show active">
                                                <p>
                                                    <div className="form-group">
                                                        <textarea 
                                                            value={hrComments} 
                                                            onChange={(e) => setHrComments(e.target.value)} 
                                                            cols="85" 
                                                            rows="10"
                                                        ></textarea>
                                                        <div className="upload_box">
                                                            Support Required
                                                            <label className="fancy-radio custom-color-green"><input type="radio" value={0} checked={isSupport === 0} onChange={() => setIsSupport(0)} /> No</label>
                                                            <label className="fancy-radio custom-color-green"><input type="radio" value={1} checked={isSupport === 1} onChange={() => setIsSupport(1)} /> Yes</label>
                                                            <label className="fancy-radio custom-color-green"><input type="radio" value={2} checked={isSupport === 2} onChange={() => setIsSupport(2)} /> Done</label>
                                                        </div>
                                                        <button 
                                                            className="btn btn-success comment_submit" 
                                                            style={{ verticalAlign:"top", margin: "0 0 0 10px" }}
                                                            disabled={inActive}
                                                            onClick={() => updateRemarks(3)}
                                                        >
                                                            Post
                                                        </button>
                                                    </div>
                                                    <div className="media">
                                                        {commentList.filter(c => c.ReplyType === 3).map((c, idx) => (
                                                            <div key={idx} className="media-body">
                                                                <span className="name">{c.Name}({c.User?.EmployeeID}) <font>{c.CreatedOn}</font></span>
                                                                <span className="message">{c.Comment}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </p>
                                            </div>
                                        </>)}
                                        {activeTab === 3 && (
                                            <div className="tab-pane show active">
                                                <div className="table-responsive">
                                                    <table className="table m-b-0">
                                                        <thead>
                                                            <tr>
                                                                <th>FieldName</th>
                                                                <th>OldValue</th>
                                                                <th>NewValue</th>
                                                                <th>CreatedBy</th>
                                                                <th>CreatedOn</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {ticketLog.map((log, index) => (
                                                                <tr key={index}>
                                                                    <td>{log.FieldName}</td>
                                                                    <td>{log.OldValue}</td>
                                                                    <td>{log.NewValue}</td>
                                                                    <td>{log.CreatedByName}</td>
                                                                    <td>{formatDate(log.CreatedOn)}</td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TicketDetails;