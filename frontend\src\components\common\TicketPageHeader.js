import React from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Grid2 as Grid,
    <PERSON>po<PERSON>,
    TextField,
    MenuItem,
    InputLabel,
    Select,
    FormControl,
    Paper,
    Grow,
    Stack,
    IconButton,
    Tooltip
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    Search as SearchIcon,
    FilterList as FilterListIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';

// Import SCSS files for styling
import '../../styles/main.scss';

const TicketPageHeader = ({
    title,
    subtitle,
    icon: IconComponent,
    activeSearchType,
    setActiveSearchType,
    resetFilters,
    // Search form props
    fromDate,
    setFromDate,
    toDate,
    setToDate,
    selected,
    setSelected,
    ticketId,
    setTicketId,
    source,
    issueSubIssue,
    statusList,
    ProductOptions,
    onSearch,
    showProductField = false,
    searchButtonText = "Search Tickets"
}) => {
    return (
        <>
            {/* Header Section */}
            <Paper elevation={0} className="tickets-header">
                <Box className="header-decoration-1"></Box>
                <Box className="header-decoration-2"></Box>
                <Grid container spacing={2} alignItems="center" className="header-content">
                    <Grid size={{ xs: 12, md: 8 }}>
                        <Stack direction="row" spacing={2} alignItems="center">
                            {IconComponent && <IconComponent className="header-icon" />}
                            <Box>
                                <Typography variant="h4" className="header-title">
                                    {title}
                                </Typography>
                                <Typography variant="body1" className="header-subtitle">
                                    {subtitle}
                                </Typography>
                            </Box>
                        </Stack>
                    </Grid>
                    <Grid size={{ xs: 12, md: 4 }}>
                        <Stack direction="row" spacing={2} className="header-btns">
                            <Button
                                variant={activeSearchType === 2 ? "contained" : "outlined"}
                                startIcon={<DashboardIcon />}
                                onClick={() => {
                                    setActiveSearchType(2);
                                    resetFilters();
                                }}
                                className={`header-btn ${activeSearchType === 2 ? 'active' : ''}`}
                            >
                                Dashboard
                            </Button>
                            <Button
                                variant={activeSearchType === 1 ? "contained" : "outlined"}
                                startIcon={<SearchIcon />}
                                onClick={() => setActiveSearchType(1)}
                                className={`header-btn ${activeSearchType === 1 ? 'active' : ''}`}
                            >
                                Search
                            </Button>
                        </Stack>
                    </Grid>
                </Grid>
            </Paper>

            {/* Search Form */}
            {activeSearchType === 1 && (
                <Grow in timeout={1000}>
                    <Card elevation={0} className="search-form-card">
                        <CardContent className="search-form-content">
                            <Stack direction="row" spacing={2} alignItems="center" className="search-form-header">
                                <FilterListIcon className="filter-icon" />
                                <Typography variant="h6" className="search-form-title">
                                    Advanced Search Filters
                                </Typography>
                                <Tooltip title="Refresh Filters">
                                    <IconButton 
                                        onClick={resetFilters}
                                        className="refresh-btn"
                                        size="small"
                                    >
                                        <RefreshIcon />
                                    </IconButton>
                                </Tooltip>
                            </Stack>

                            <Grid container spacing={3}>
                                {/* Date Range */}
                                <Grid size={{ xs: 12, md: 3 }}>
                                    <TextField
                                        label="From Date"
                                        type="date"
                                        fullWidth
                                        value={fromDate.toISOString().split('T')[0]}
                                        onChange={(e) => setFromDate(new Date(e.target.value))}
                                        InputLabelProps={{ shrink: true }}
                                        className="form-field"
                                    />
                                </Grid>

                                <Grid size={{ xs: 12, md: 3 }}>
                                    <TextField
                                        label="To Date"
                                        type="date"
                                        fullWidth
                                        value={toDate.toISOString().split('T')[0]}
                                        onChange={(e) => setToDate(new Date(e.target.value))}
                                        InputLabelProps={{ shrink: true }}
                                        className="form-field"
                                    />
                                </Grid>

                                {/* Process */}
                                <Grid size={{ xs: 12, md: 3 }}>
                                    <FormControl fullWidth className="form-field">
                                        <InputLabel>Process</InputLabel>
                                        <Select
                                            label="Process"
                                            value={selected.Source?.SourceID || 0}
                                            onChange={(e) => setSelected(prev => ({
                                                ...prev,
                                                Source: { SourceID: parseInt(e.target.value) }
                                            }))}
                                        >
                                            {source.map(s => (
                                                <MenuItem key={s.SourceID} value={s.SourceID}>
                                                    {s.Name}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>

                               
                                {showProductField && selected.Source?.SourceID && [2, 4, 5, 8].includes(selected.Source?.SourceID) ? (
                                    <Grid size={{ xs: 12, md: 3 }}>
                                        <FormControl fullWidth className="form-field">
                                            <InputLabel>Product</InputLabel>
                                            <Select
                                                label="Product"
                                                value={selected.Product?.ProductID || 0}
                                                onChange={(e) => setSelected(prev => ({
                                                    ...prev,
                                                    Product: { ProductID: parseInt(e.target.value) }
                                                }))}
                                            >
                                                {ProductOptions?.map(p => (
                                                    <MenuItem key={p.ProductID} value={p.ProductID}>
                                                        {p.Name}
                                                    </MenuItem>
                                                ))}
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                ) : null}

                                {/* Feedback */}
                                <Grid size={{ xs: 12, md: 3 }}>
                                    <FormControl fullWidth className="form-field">
                                        <InputLabel>Feedback</InputLabel>
                                        <Select
                                            label="Feedback"
                                            value={selected.IssueType?.IssueID || ''}
                                            onChange={(e) => setSelected(prev => ({
                                                ...prev,
                                                IssueType: { IssueID: parseInt(e.target.value) }
                                            }))}
                                        >
                                            <MenuItem value="">Select Feedback</MenuItem>
                                            {issueSubIssue
                                                .filter(item => item.SourceID === selected.Source?.SourceID)
                                                .map(issue => (
                                                    <MenuItem key={issue.IssueID} value={issue.IssueID}>
                                                        {issue.ISSUENAME}
                                                    </MenuItem>
                                                ))}
                                        </Select>
                                    </FormControl>
                                </Grid>

                                {/* Status */}
                                <Grid size={{ xs: 12, md: 3 }}>
                                    <FormControl fullWidth className="form-field">
                                        <InputLabel>Status</InputLabel>
                                        <Select
                                            label="Status"
                                            value={selected.Status?.StatusID || ''}
                                            onChange={(e) => setSelected(prev => ({
                                                ...prev,
                                                Status: { StatusID: parseInt(e.target.value) }
                                            }))}
                                        >
                                            <MenuItem value="">Select Status</MenuItem>
                                            {statusList.map(status => (
                                                <MenuItem key={status.StatusID} value={status.StatusID}>
                                                    {status.StatusName}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>

                                {/* Feedback ID */}
                                <Grid size={{ xs: 12, md: 3 }}>
                                    <TextField
                                        label="Feedback ID"
                                        fullWidth
                                        value={ticketId}
                                        onChange={(e) => setTicketId(e.target.value)}
                                        placeholder="Enter Feedback ID"
                                        className="form-field"
                                    />
                                </Grid>

                                {/* Action Buttons */}
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <Stack direction="row" spacing={2} className="action-buttons">
                                        <Button
                                            variant="contained"
                                            startIcon={<SearchIcon />}
                                            onClick={onSearch}
                                            className="search-btn"
                                        >
                                            {searchButtonText}
                                        </Button>
                                        <Button
                                            variant="outlined"
                                            startIcon={<RefreshIcon />}
                                            onClick={resetFilters}
                                            className="reset-btn"
                                        >
                                            Reset Filters
                                        </Button>
                                    </Stack>
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                </Grow>
            )}
        </>
    );
};

export default TicketPageHeader;
