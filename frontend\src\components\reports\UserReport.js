import React, { useState, useEffect } from 'react';
import api from '../../services/api';

const UserReport = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    userId: '',
    status: ''
  });

  useEffect(() => {
    fetchReports();
  }, [filters]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const response = await api.post('/api/GetUserActivityReport', filters);
      setReports(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch reports');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const exportToExcel = async () => {
    try {
      const response = await api.post('/api/ExportUserReport', filters, {
        responseType: 'blob'
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'UserReport.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('Failed to export report');
    }
  };

  if (loading) return <div className="loading">Loading...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="report-container">
      <div className="report-header">
        <h1>User Activity Report</h1>
        <div className="report-filters">
          <div className="date-filters">
            <input
              type="date"
              name="startDate"
              value={filters.startDate}
              onChange={handleFilterChange}
              className="date-input"
            />
            <span>to</span>
            <input
              type="date"
              name="endDate"
              value={filters.endDate}
              onChange={handleFilterChange}
              className="date-input"
            />
          </div>
          <input
            type="text"
            name="userId"
            placeholder="User ID"
            value={filters.userId}
            onChange={handleFilterChange}
            className="filter-input"
          />
          <select
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            className="filter-select"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <button onClick={exportToExcel} className="export-btn">
            <i className="fas fa-file-excel"></i> Export
          </button>
        </div>
      </div>

      <div className="report-table-container">
        <table className="report-table">
          <thead>
            <tr>
              <th>User ID</th>
              <th>Name</th>
              <th>Department</th>
              <th>Role</th>
              <th>Tickets Created</th>
              <th>Tickets Resolved</th>
              <th>Avg Response Time</th>
              <th>Last Active</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {reports.map(report => (
              <tr key={report.UserId}>
                <td>{report.EmployeeId}</td>
                <td>{report.Name}</td>
                <td>{report.Department}</td>
                <td>{report.Role}</td>
                <td>{report.TicketsCreated}</td>
                <td>{report.TicketsResolved}</td>
                <td>{report.AvgResponseTime}</td>
                <td>{new Date(report.LastActive).toLocaleString()}</td>
                <td>
                  <span className={`status-badge ${report.Status.toLowerCase()}`}>
                    {report.Status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UserReport; 