const BUILD_ENVIRONMENT = process.env.REACT_APP_BUILD_ENV || 'live';

function getApiBaseUrl() {
    let baseUrl = 'https://matrixcoreapi.policybazaar.com/feedback/';
    
    switch (BUILD_ENVIRONMENT.toLowerCase()) {
        case 'live':
            baseUrl = 'https://matrixcoreapi.policybazaar.com/feedback/';
            break;
        case 'qa':
            baseUrl = 'https://localhost:7219/feedback/';
            break;
        default:
            baseUrl = "https://matrixcoreapi.policybazaar.com/feedback/"
            break;
    }
    
    return baseUrl;
}

export const CONFIG = {
    API_BASE_URL: getApiBaseUrl(),
    BUILD_ENV: BUILD_ENVIRONMENT
}