import Axios from 'axios'
import { getAsteriskToken, getUserId, clearAuthData } from './CommonHelper';
import { CONFIG } from '../config';

export const base_url = CONFIG.API_BASE_URL;
let axiosInstance = Axios.create({
    baseURL: base_url,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

axiosInstance.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        if (error.response && error.response.status === 401) {
           // clearAuthData();

            if (window.location.pathname !== '/login') {
                window.location.href = '/login';
            }
        }
        return Promise.reject(error);
    }
);


export const getHeaders = (Headers = null, Source) => {
    
    Headers = Headers || {
        "AgentId": getUserId() || 0,
        "source": Source || "matrix",
        "Token": getAsteriskToken(),
        "Content-Type": "application/json"
    }

    return Headers || {};
}



export const CALL_API = async (input) => {

    // Set defaults
    input.timeout = input.timeout || "m";
    input.method = input.method || "GET";
    input.cache = input.cache || false;

    let Headers = getHeaders(input.Headers, input.source);

    // TIMEOUT
    const SV_CONFIG = {
        "timeout": {
            "s": 1000,
            "m": 3000,
            "l": 10000
        }
    }

    let timeout = 0;
    try {
        timeout = SV_CONFIG["timeout"][input.timeout] || parseInt(input.timeout) || 3000;
    } catch {
        timeout = 3000;
    }

    // Prepare reqData
    var reqData = {
        method: input.method,
        url: input.url,
        headers: Headers,
        cache: input.cache,
        timeout,
        withCredentials: true
    };

    // Add payload if provided
    if (input.requestData !== undefined) { reqData.data = JSON.stringify(input.requestData); }
    if (input.formData) { reqData.data = input.formData; }

    try {
        const response = await axiosInstance(reqData);
        if (input.acceptOnly200 === true && response.status !== 200) {
            throw new Error(response.statusText);
        }
        return response.data;
    } catch (error) {
        console.error('API Error:', error.message);
        throw error;
    }
}