/* Main SCSS File - All Styles Combined */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');

// Variables
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$success-color: #22c55e;
$info-color: #3b82f6;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}

// Main Layout Container
.assigned-tickets-main {
  min-height: 100vh;
  background-color: $background-color !important;
  padding: 0.5rem;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

// Header Section
.tickets-header {
  padding: 0.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%) !important;
  color: $white !important;
  position: relative;
  overflow: hidden;
  @include card-shadow;
  border: none !important;
  display: block !important;
  width: 100% !important;
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba($white, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba($white, 0.19);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: $white !important;
}

.header-title {
  font-weight: 700 !important;
  color: $white !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-btns {
  justify-content: flex-end;
}

.header-subtitle {
  opacity: 0.9;
  color: $white !important;
  display: block !important;
  visibility: visible !important;
}

// Ensure header text is visible
.tickets-header .header-title,
.tickets-header .MuiTypography-h4 {
  color: $white !important;
  font-size: 32px;
  font-family: roboto;
}

.tickets-header .header-subtitle,
.tickets-header .MuiTypography-body1 {
  color: $white !important;
  opacity: 0.9;
}

// Header Buttons
.header-btn {
  color: $white !important;
  border-color: rgba($white, 0.5) !important;
  background-color: transparent !important;
  @include transition-smooth;

  &:hover {
    background-color: rgba($white, 0.1) !important;
    border-color: $white !important;
  }

  &.active {
    color: $primary-color !important;
    background-color: $white !important;

    &:hover {
      background-color: $background-color !important;
    }
  }
}

// Search Form
.search-form-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
  margin-bottom: 2rem !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.search-form-content {
  padding: 2rem !important;
  display: block !important;
  visibility: visible !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
  display: flex !important;
  visibility: visible !important;
}

.filter-icon {
  color: $primary-color !important;
}

.search-form-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

// Form Field Styling
.form-field {
  .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
    background-color: $white !important;
    @include transition-smooth;

    &:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
      }
    }

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
        border-width: 2px !important;
      }
    }
  }

  .MuiInputLabel-root {
    color: $text-secondary !important;
    font-weight: 500 !important;

    &.Mui-focused {
      color: $info-color !important;
    }
  }
}

// Search and Reset Buttons
.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: $info-color !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;

  &:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  }
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: $text-secondary !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    border-color: $info-color !important;
    background-color: $background-color !important;
    color: $info-color !important;
  }
}

// Data Table Card
.data-table-card {
  background-color: transparent !important;
}

.table-card-content {
  padding: 0 !important;
}

.table-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: $success-color !important;
  color: $success-color !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    background-color: $success-color !important;
    color: $white !important;
    transform: translateY(-1px);
  }
}

.table-content {
  padding: 1.5rem 0px !important;

  &.modern-table-wrapper {
    padding: 0 !important;
    margin-top: 1.5rem !important;

    // Add subtle animation when table loads
    .MuiTableContainer-root {
      animation: slideUp 0.6s ease-out;
    }
  }
}

// Dashboard Stats (Feedback Stats)
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;

  .stat-card {
    background: $white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    @include card-shadow;
    @include transition-smooth;
    cursor: pointer;
    border: 1px solid $border-color;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $white;
      opacity: 0.9;
    }

    // Status-specific styling
    &.new-status {
      background: linear-gradient(135deg, #3999ee 0%, #00f2fe 100%);
    }

    &.open-status {
      background: linear-gradient(135deg, #efa891 0%, #ffecd2 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.tat-status {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.resolved-status {
      background: linear-gradient(135deg, #79d2cf 0%, #f9c7d7 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.closed-status {
      background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    }
  }
}

// MyFeedback specific styles
.my-feedback {
  padding: 0;
}

.loading {
  @include flex-center;
  min-height: 200px;
  font-size: 14px;
  color: $text-secondary;
}

// 🎨 SUPER CREATIVE MODERN TABLE DESIGN 🎨
.creative-table-wrapper {
  position: relative;
  margin: 2rem 0;
  animation: tableSlideIn 0.8s ease-out;
}

.creative-table-container {
  background: $white !important;
  border-radius: 2rem !important;
  overflow: hidden !important;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.1),
    0 8px 25px rgba(102, 126, 234, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
  position: relative !important;
  @include transition-smooth;

  // Animated rainbow border
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
      #667eea 0%,
      #764ba2 25%,
      #3b82f6 50%,
      #10b981 75%,
      #f59e0b 100%);
    border-radius: 2rem;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite alternate;
  }

  // Floating particles effect
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    pointer-events: none;
    animation: particleFloat 6s ease-in-out infinite;
  }

  &:hover {
    transform: translateY(-5px) scale(1.01);
    box-shadow:
      0 30px 80px rgba(0, 0, 0, 0.15),
      0 15px 40px rgba(102, 126, 234, 0.25);
  }
}

// 🌟 CREATIVE TABLE STYLING 🌟
.creative-table {
  background: transparent !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
  width: 100% !important;
  position: relative !important;
}

// 🎭 MAGICAL HEADER DESIGN 🎭
.creative-table-head {
  background: linear-gradient(135deg,
    #1a202c 0%,
    #2d3748 25%,
    #4a5568 50%,
    #2d3748 75%,
    #1a202c 100%) !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(102, 126, 234, 0.1) 25%,
      rgba(59, 130, 246, 0.1) 50%,
      rgba(118, 75, 162, 0.1) 75%,
      transparent 100%);
    animation: headerShimmer 4s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
      #667eea 0%,
      #764ba2 25%,
      #3b82f6 50%,
      #10b981 75%,
      #f59e0b 100%);
    animation: rainbowFlow 3s linear infinite;
  }
}

.creative-header-row {
  position: relative !important;
  z-index: 2 !important;
}

.creative-header-cell {
  background: transparent !important;
  border: none !important;
  padding: 1.5rem 1.25rem !important;
  position: relative !important;
  overflow: hidden !important;

  &.creative-header-cell-first {
    padding-left: 2rem !important;
  }

  &.creative-header-cell-last {
    padding-right: 2rem !important;
  }

  // Separator lines between headers
  &:not(.creative-header-cell-last)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: linear-gradient(to bottom,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%);
  }

  .header-text {
    color: $white !important;
    font-weight: 700 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    position: relative !important;
    z-index: 3 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    display: inline-block !important;
    @include transition-smooth;
  }

  .header-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 1;
  }

  &:hover {
    .header-text {
      transform: translateY(-2px);
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    .header-decoration {
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
    }
  }
}

// 🎪 CREATIVE TABLE BODY 🎪
.creative-table-body {
  background: transparent !important;
  position: relative !important;
}

.creative-body-row {
  @include transition-smooth;
  border: none !important;
  position: relative !important;
  background: $white !important;

  &.row-even {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  }

  &.row-odd {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  }

  // Hover magic ✨
  &:hover {
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.08) 0%,
      rgba(59, 130, 246, 0.08) 50%,
      rgba(118, 75, 162, 0.08) 100%) !important;
    transform: translateY(-3px) scale(1.005) !important;
    box-shadow:
      0 8px 30px rgba(0, 0, 0, 0.1),
      0 4px 15px rgba(102, 126, 234, 0.2) !important;
    z-index: 10 !important;

    .creative-body-cell {
      border-color: rgba(102, 126, 234, 0.3) !important;

      &::before {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .cell-content {
      transform: translateY(-1px);
      color: $text-primary !important;
    }
  }

  // Row entrance animation
  &:nth-child(1) { animation: rowSlideIn 0.6s ease-out 0.1s both; }
  &:nth-child(2) { animation: rowSlideIn 0.6s ease-out 0.2s both; }
  &:nth-child(3) { animation: rowSlideIn 0.6s ease-out 0.3s both; }
  &:nth-child(4) { animation: rowSlideIn 0.6s ease-out 0.4s both; }
  &:nth-child(5) { animation: rowSlideIn 0.6s ease-out 0.5s both; }
  &:nth-child(n+6) { animation: rowSlideIn 0.6s ease-out 0.6s both; }
}

.creative-body-cell {
  padding: 1.25rem 1rem !important;
  border: none !important;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8) !important;
  position: relative !important;
  overflow: hidden !important;
  @include transition-smooth;

  &.creative-body-cell-first {
    padding-left: 2rem !important;
    border-left: 3px solid transparent !important;
    @include transition-smooth;
  }

  &.creative-body-cell-last {
    padding-right: 2rem !important;
  }

  // Subtle cell highlight effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(102, 126, 234, 0.05) 50%,
      transparent 100%);
    transition: all 0.5s ease;
    opacity: 0;
  }

  .cell-content {
    position: relative !important;
    z-index: 2 !important;
    font-size: 0.875rem !important;
    color: $text-primary !important;
    font-weight: 500 !important;
    @include transition-smooth;
    display: inline-block !important;

    &.date-content {
      color: $text-secondary !important;
      font-family: 'Roboto Mono', monospace !important;
    }

    &.employee-id {
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%) !important;
      color: #4338ca !important;
      padding: 0.25rem 0.5rem !important;
      border-radius: 0.5rem !important;
      font-weight: 600 !important;
      font-size: 0.75rem !important;
    }

    &.role-content {
      color: #059669 !important;
      font-weight: 600 !important;
    }

    &.bu-content {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
      color: #92400e !important;
      padding: 0.25rem 0.5rem !important;
      border-radius: 0.5rem !important;
      font-weight: 600 !important;
      font-size: 0.75rem !important;
    }

    &.assign-content {
      color: #7c3aed !important;
    }

    &.process-content {
      color: #dc2626 !important;
      font-weight: 600 !important;
    }

    &.issue-content {
      color: #ea580c !important;
    }
  }
}

// 🔗 MAGICAL FEEDBACK LINKS 🔗
.creative-feedback-link {
  color: $white !important;
  text-decoration: none !important;
  font-weight: 700 !important;
  padding: 0.75rem 1.25rem !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, $primary-color 0%, $info-color 100%) !important;
  border: 2px solid transparent !important;
  display: inline-block !important;
  position: relative !important;
  overflow: hidden !important;
  @include transition-smooth;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;

  .link-text {
    position: relative !important;
    z-index: 3 !important;
    font-size: 0.875rem !important;
    letter-spacing: 0.5px !important;
  }

  // Shimmer effect
  .link-shimmer {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%);
    transition: left 0.6s ease;
    z-index: 2;
  }

  // Glow effect
  .link-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.5) 0%,
      rgba(59, 130, 246, 0.5) 100%);
    border-radius: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    filter: blur(4px);
  }

  &:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow:
      0 8px 25px rgba(102, 126, 234, 0.4),
      0 15px 35px rgba(0, 0, 0, 0.1) !important;
    background: linear-gradient(135deg, #5a67d8 0%, #2563eb 100%) !important;
    text-decoration: none !important;

    .link-shimmer {
      left: 100%;
    }

    .link-glow {
      opacity: 1;
    }

    .link-text {
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  }

  &:focus {
    outline: none !important;
    box-shadow:
      0 0 0 3px rgba(102, 126, 234, 0.4),
      0 8px 25px rgba(102, 126, 234, 0.3) !important;
  }
}

// 🏷️ SUPER CREATIVE STATUS CHIPS 🏷️
.creative-status-chip {
  font-weight: 700 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.8px !important;
  border-radius: 1.5rem !important;
  padding: 0.5rem 1rem !important;
  border: 2px solid transparent !important;
  position: relative !important;
  overflow: hidden !important;
  @include transition-smooth;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  // Pulsing animation for active statuses
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    transform: translateY(-50%);
    animation: statusPulse 2s ease-in-out infinite;
  }

  .MuiChip-label {
    padding-left: 1.5rem !important;
    font-weight: inherit !important;
    font-size: inherit !important;
    text-transform: inherit !important;
    letter-spacing: inherit !important;
  }

  &.status-new {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: $white !important;
    border-color: #1d4ed8 !important;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4) !important;
    }
  }

  &.status-open {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
    color: $white !important;
    border-color: #d97706 !important;

    &:hover {
      background: linear-gradient(135deg, #e5890b 0%, #c2670a 100%) !important;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4) !important;
    }
  }

  &.status-resolved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: $white !important;
    border-color: #059669 !important;

    &:hover {
      background: linear-gradient(135deg, #0ea574 0%, #047857 100%) !important;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4) !important;
    }
  }

  &.status-closed {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
    color: $white !important;
    border-color: #4b5563 !important;

    &:hover {
      background: linear-gradient(135deg, #5b6470 0%, #3f4651 100%) !important;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4) !important;
    }
  }

  &.status-pending {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
    color: $white !important;
    border-color: #7c3aed !important;

    &:hover {
      background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4) !important;
    }
  }
}

// 📋 CREATIVE NO RECORDS DESIGN 📋
.creative-no-records {
  text-align: center !important;
  padding: 4rem 2rem !important;
  background: linear-gradient(135deg,
    #f8fafc 0%,
    #f1f5f9 50%,
    #e2e8f0 100%) !important;
  border-radius: 2rem !important;
  margin: 2rem !important;
  border: 3px dashed transparent !important;
  background-clip: padding-box !important;
  position: relative !important;
  overflow: hidden !important;
  animation: noRecordsPulse 3s ease-in-out infinite !important;

  // Animated dashed border
  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg,
      #667eea 0%,
      #764ba2 25%,
      #3b82f6 50%,
      #10b981 75%,
      #f59e0b 100%);
    border-radius: 2rem;
    z-index: -1;
    animation: borderRotate 4s linear infinite;
  }

  // Floating background elements
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
    animation: backgroundFloat 6s ease-in-out infinite;
    pointer-events: none;
  }

  .no-records-icon {
    font-size: 4rem !important;
    margin-bottom: 1.5rem !important;
    display: block !important;
    animation: iconBounce 2s ease-in-out infinite !important;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1)) !important;
  }

  .no-records-title {
    color: $text-primary !important;
    font-weight: 700 !important;
    margin-bottom: 1rem !important;
    font-size: 1.5rem !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    z-index: 2 !important;
  }

  .no-records-subtitle {
    color: $text-secondary !important;
    font-size: 1rem !important;
    opacity: 0.8 !important;
    position: relative !important;
    z-index: 2 !important;
    max-width: 400px !important;
    margin: 0 auto !important;
    line-height: 1.6 !important;
  }
}

// 🎬 CREATIVE ANIMATIONS 🎬
@keyframes tableSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes borderGlow {
  0% {
    opacity: 0.5;
    filter: hue-rotate(0deg);
  }
  100% {
    opacity: 1;
    filter: hue-rotate(45deg);
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes headerShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes rainbowFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

@keyframes rowSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.2);
  }
}

@keyframes noRecordsPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes borderRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(180deg);
  }
}

@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 📱 RESPONSIVE CREATIVE TABLE DESIGN 📱
@media (max-width: 1200px) {
  .creative-table-container {
    margin: 1.5rem 0;
    border-radius: 1.5rem !important;

    &::before {
      border-radius: 1.5rem;
    }
  }

  .creative-header-cell {
    padding: 1.25rem 1rem !important;

    .header-text {
      font-size: 0.8rem !important;
    }
  }

  .creative-body-cell {
    padding: 1rem 0.875rem !important;

    .cell-content {
      font-size: 0.8rem !important;
    }
  }

  .creative-feedback-link {
    padding: 0.625rem 1rem !important;

    .link-text {
      font-size: 0.8rem !important;
    }
  }
}

@media (max-width: 768px) {
  .creative-table-wrapper {
    margin: 1rem 0;
  }

  .creative-table-container {
    margin: 1rem 0;
    border-radius: 1.25rem !important;
    overflow-x: auto !important;

    &::before {
      border-radius: 1.25rem;
    }

    &:hover {
      transform: translateY(-2px) scale(1.005);
    }
  }

  .creative-table {
    min-width: 800px !important; // Ensure horizontal scroll
  }

  .creative-header-cell {
    padding: 1rem 0.75rem !important;

    &.creative-header-cell-first {
      padding-left: 1.5rem !important;
    }

    &.creative-header-cell-last {
      padding-right: 1.5rem !important;
    }

    .header-text {
      font-size: 0.75rem !important;
      letter-spacing: 0.5px !important;
    }
  }

  .creative-body-cell {
    padding: 0.875rem 0.75rem !important;

    &.creative-body-cell-first {
      padding-left: 1.5rem !important;
    }

    &.creative-body-cell-last {
      padding-right: 1.5rem !important;
    }

    .cell-content {
      font-size: 0.75rem !important;

      &.employee-id,
      &.bu-content {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.7rem !important;
      }
    }
  }

  .creative-feedback-link {
    padding: 0.5rem 0.875rem !important;
    border-radius: 0.875rem !important;

    .link-text {
      font-size: 0.75rem !important;
      letter-spacing: 0.3px !important;
    }

    &:hover {
      transform: translateY(-2px) scale(1.03) !important;
    }
  }

  .creative-status-chip {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.7rem !important;
    border-radius: 1.25rem !important;

    &::before {
      width: 6px;
      height: 6px;
    }

    .MuiChip-label {
      padding-left: 1.25rem !important;
    }
  }

  .creative-no-records {
    padding: 3rem 1.5rem !important;
    margin: 1.5rem !important;
    border-radius: 1.5rem !important;

    .no-records-icon {
      font-size: 3rem !important;
      margin-bottom: 1rem !important;
    }

    .no-records-title {
      font-size: 1.25rem !important;
    }

    .no-records-subtitle {
      font-size: 0.875rem !important;
    }
  }
}

@media (max-width: 480px) {
  .creative-table {
    min-width: 600px !important;
  }

  .creative-header-cell {
    padding: 0.875rem 0.5rem !important;

    .header-text {
      font-size: 0.7rem !important;
    }
  }

  .creative-body-cell {
    padding: 0.75rem 0.5rem !important;

    .cell-content {
      font-size: 0.7rem !important;
    }
  }

  .creative-feedback-link {
    padding: 0.4rem 0.75rem !important;

    .link-text {
      font-size: 0.7rem !important;
    }
  }

  .creative-status-chip {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.65rem !important;
  }
}

.table-responsive {
  border-radius: 1.5rem;
  overflow: hidden;
  background: $white;
  margin: 0;
  padding: 0;

  .table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
    background: $white;
    border-radius: 1.5rem;
    overflow: hidden;
    font-family: 'Roboto', sans-serif;

    &.table-custom {
      box-shadow: none;
      border: none;
    }
  }
}

// Modern Table Header Design
.table thead {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, $primary-color 0%, $secondary-color 50%, $info-color 100%);
  }

  th {
    background: transparent !important;
    color: $white !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 1.25rem 1rem !important;
    border: none !important;
    position: relative;

    &:first-child {
      padding-left: 1.5rem !important;
    }

    &:last-child {
      padding-right: 1.5rem !important;
    }

    // Add subtle separators between headers
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 25%;
      bottom: 25%;
      width: 1px;
      background: rgba($white, 0.2);
    }
  }
}

// Modern Table Body Design
.table tbody {
  tr {
    @include transition-smooth;
    border: none !important;
    background: $white;

    &:nth-child(even) {
      background: #f8fafc;
    }

    &:hover {
      background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($info-color, 0.05) 100%) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

      td {
        border-color: rgba($primary-color, 0.2) !important;
      }
    }

    &:last-child td {
      border-bottom: none !important;
    }
  }

  td {
    padding: 1rem !important;
    border: none !important;
    border-bottom: 1px solid #e2e8f0 !important;
    font-size: 0.875rem !important;
    color: $text-primary !important;
    vertical-align: middle !important;
    @include transition-smooth;

    &:first-child {
      padding-left: 1.5rem !important;
      font-weight: 600;
      color: $primary-color;
    }

    &:last-child {
      padding-right: 1.5rem !important;
    }
  }
}

// Enhanced Feedback Link Styling
.feedback-link {
  color: $primary-color !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($info-color, 0.1) 100%) !important;
  border: 1px solid rgba($primary-color, 0.2) !important;
  display: inline-block !important;
  @include transition-smooth;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba($white, 0.4), transparent);
    transition: left 0.5s;
  }

  &:hover {
    color: $white !important;
    background: linear-gradient(135deg, $primary-color 0%, $info-color 100%) !important;
    border-color: $primary-color !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba($primary-color, 0.3);
    text-decoration: none !important;

    &::before {
      left: 100%;
    }
  }

  &:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3) !important;
  }
}

// Status Badge Styling
.status-badge {
  padding: 0.375rem 0.75rem !important;
  border-radius: 1rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  @include transition-smooth;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
  }

  // Status-specific colors
  &.status-new {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-open {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-resolved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-closed {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-pending {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }
}

// Enhanced No Records Message
.no-records {
  text-align: center !important;
  padding: 4rem 2rem !important;
  color: $text-secondary !important;
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-radius: 1rem !important;
  margin: 2rem !important;
  border: 2px dashed $border-color !important;
  position: relative !important;

  &::before {
    content: '📋';
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  &::after {
    content: 'Try adjusting your search criteria or create a new record.';
    display: block;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    opacity: 0.7;
  }
}

// Table Loading Animation
.table-loading {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($white, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
}

// Responsive Table Design
@media (max-width: 1200px) {
  .table thead th {
    padding: 1rem 0.75rem !important;
    font-size: 0.8rem !important;
  }

  .table tbody td {
    padding: 0.875rem 0.75rem !important;
    font-size: 0.8rem !important;
  }
}

@media (max-width: 768px) {
  .modern-table-container {
    margin: 1rem 0;
    border-radius: 1rem;
  }

  .table-responsive {
    border-radius: 1rem;

    .table {
      border-radius: 1rem;
      min-width: 600px; // Ensure horizontal scroll on mobile
    }
  }

  .table thead th {
    padding: 0.875rem 0.5rem !important;
    font-size: 0.75rem !important;

    &:first-child {
      padding-left: 1rem !important;
    }

    &:last-child {
      padding-right: 1rem !important;
    }
  }

  .table tbody td {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.75rem !important;

    &:first-child {
      padding-left: 1rem !important;
    }

    &:last-child {
      padding-right: 1rem !important;
    }
  }

  .feedback-link {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .status-badge {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
  }
}

// MUI Table Enhancements
.MuiTableContainer-root {
  &.modern-table-container {
    border-radius: 1.5rem !important;
    overflow: hidden !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid $border-color !important;
    background: $white !important;
  }
}

// Enhanced MUI Paper styling for tables
.MuiPaper-root {
  &.modern-table-paper {
    border-radius: 1.5rem !important;
    overflow: hidden !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08) !important;
  }
}

// MUI Chip enhancements for status badges
.MuiChip-root {
  &.status-chip {
    border-radius: 1rem !important;
    font-weight: 600 !important;
    font-size: 0.75rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;

    .MuiChip-label {
      padding: 0.375rem 0.75rem !important;
    }
  }
}

// Table animation enhancements
@keyframes tableRowHover {
  0% {
    transform: translateY(0);
    box-shadow: none;
  }
  100% {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
}

@keyframes linkShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// Additional modern table utilities
.table-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.table-slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark mode support (optional)
@media (prefers-color-scheme: dark) {
  .modern-table-container {
    background: #1a202c !important;
    border-color: #2d3748 !important;
  }

  .table thead th {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
  }

  .table tbody tr {
    background: #1a202c !important;

    &:nth-child(even) {
      background: #2d3748 !important;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%) !important;
    }
  }

  .table tbody td {
    color: #e2e8f0 !important;
    border-color: #4a5568 !important;
  }
}

// CreateFeedback Header Styles
.feedback-header {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%) !important;
  border-radius: 1rem !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  position: relative;
  overflow: hidden;
  color: $white !important;
  @include card-shadow;

  // Decorative Elements
  .feedback-header-decoration-1 {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba($white, 0.1);
    z-index: 1;
  }

  .feedback-header-decoration-2 {
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba($white, 0.05);
    z-index: 1;
  }

  .feedback-header-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .feedback-header-icon {
    font-size: 3rem;
    color: $white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba($white, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);

    svg {
      font-size: 2.5rem !important;
      color: $white !important;
    }
  }

  .feedback-header-text {
    flex: 1;
  }

  .feedback-header-title {
    font-weight: 700 !important;
    color: $white !important;
    margin-bottom: 0.5rem !important;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .feedback-header-badge {
    background: rgba($white, 0.2) !important;
    color: $white !important;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .feedback-header-subtitle {
    color: rgba($white, 0.9) !important;
    margin-bottom: 1rem !important;
  }

  .feedback-header-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
  }

  .feedback-quick-report-btn,
  .feedback-support-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid rgba($white, 0.3) !important;
    background: rgba($white, 0.1) !important;
    color: $white !important;
    text-decoration: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    @include transition-smooth;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba($white, 0.2) !important;
      border-color: rgba($white, 0.5) !important;
      transform: translateY(-2px);
      color: $white !important;
      text-decoration: none;
    }

    svg {
      font-size: 1rem !important;
      color: $white !important;
    }
  }

  .feedback-quick-report-btn {
    cursor: pointer;
    border: none;
  }

  // Additional feedback header overrides
  * {
    color: $white !important;
  }

  .MuiTypography-root {
    color: $white !important;
  }

  .MuiSvgIcon-root {
    color: $white !important;
  }
}










