/* Main SCSS File - All Styles Combined */
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');

// Variables
$primary-color: #667eea;
$secondary-color: #764ba2;
$background-color: #f8fafc;
$white: #ffffff;
$border-color: #e2e8f0;
$text-primary: #1e293b;
$text-secondary: #64748b;
$success-color: #22c55e;
$info-color: #3b82f6;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

@mixin transition-smooth {
  transition: all 0.3s ease;
}

// Main Layout Container
.assigned-tickets-main {
  min-height: 100vh;
  background-color: $background-color !important;
  padding: 0.5rem;
  width: 100% !important;
  display: block !important;
}

.assigned-tickets-container {
  max-width: 1400px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  width: 100% !important;
}

// Header Section
.tickets-header {
  padding: 0.5rem !important;
  margin-bottom: 1.5rem !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%) !important;
  color: $white !important;
  position: relative;
  overflow: hidden;
  @include card-shadow;
  border: none !important;
  display: block !important;
  width: 100% !important;
}

.header-decoration-1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba($white, 0.1);
  z-index: 1 !important;
}

.header-decoration-2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba($white, 0.19);
  z-index: 1 !important;
}

.header-content {
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  width: 100% !important;
}

.header-icon {
  font-size: 2.5rem !important;
  color: $white !important;
}

.header-title {
  font-weight: 700 !important;
  color: $white !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  visibility: visible !important;
}

.header-btns {
  justify-content: flex-end;
}

.header-subtitle {
  opacity: 0.9;
  color: $white !important;
  display: block !important;
  visibility: visible !important;
}

// Ensure header text is visible
.tickets-header .header-title,
.tickets-header .MuiTypography-h4 {
  color: $white !important;
  font-size: 32px;
  font-family: roboto;
}

.tickets-header .header-subtitle,
.tickets-header .MuiTypography-body1 {
  color: $white !important;
  opacity: 0.9;
}

// Header Buttons
.header-btn {
  color: $white !important;
  border-color: rgba($white, 0.5) !important;
  background-color: transparent !important;
  @include transition-smooth;

  &:hover {
    background-color: rgba($white, 0.1) !important;
    border-color: $white !important;
  }

  &.active {
    color: $primary-color !important;
    background-color: $white !important;

    &:hover {
      background-color: $background-color !important;
    }
  }
}

// Search Form
.search-form-card {
  border-radius: 1rem !important;
  background-color: $white !important;
  border: 1px solid $border-color !important;
  @include card-shadow;
  margin-bottom: 2rem !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.search-form-content {
  padding: 2rem !important;
  display: block !important;
  visibility: visible !important;
}

.search-form-header {
  margin-bottom: 1.5rem !important;
  display: flex !important;
  visibility: visible !important;
}

.filter-icon {
  color: $primary-color !important;
}

.search-form-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

// Form Field Styling
.form-field {
  .MuiOutlinedInput-root {
    border-radius: 0.5rem !important;
    background-color: $white !important;
    @include transition-smooth;

    &:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
      }
    }

    &.Mui-focused {
      .MuiOutlinedInput-notchedOutline {
        border-color: $info-color !important;
        border-width: 2px !important;
      }
    }
  }

  .MuiInputLabel-root {
    color: $text-secondary !important;
    font-weight: 500 !important;

    &.Mui-focused {
      color: $info-color !important;
    }
  }
}

// Search and Reset Buttons
.search-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  background-color: $info-color !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3) !important;

  &:hover {
    background-color: #2563eb !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
  }
}

.reset-btn {
  padding: 1rem 2rem !important;
  border-radius: 0.5rem !important;
  border-color: #d1d5db !important;
  color: $text-secondary !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    border-color: $info-color !important;
    background-color: $background-color !important;
    color: $info-color !important;
  }
}

// Data Table Card
.data-table-card {
  background-color: transparent !important;
}

.table-card-content {
  padding: 0 !important;
}

.table-title {
  color: $text-primary !important;
  font-weight: 600 !important;
}

.export-btn {
  border-radius: 0.5rem !important;
  border-color: $success-color !important;
  color: $success-color !important;
  font-weight: 600 !important;
  text-transform: none !important;

  &:hover {
    background-color: $success-color !important;
    color: $white !important;
    transform: translateY(-1px);
  }
}

.table-content {
  padding: 1.5rem 0px !important;
}

// Dashboard Stats (Feedback Stats)
.feedback-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1rem 0;

  .stat-card {
    background: $white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    @include card-shadow;
    @include transition-smooth;
    cursor: pointer;
    border: 1px solid $border-color;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: $white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    p {
      font-size: 1rem;
      font-weight: 600;
      margin: 0;
      color: $white;
      opacity: 0.9;
    }

    // Status-specific styling
    &.new-status {
      background: linear-gradient(135deg, #3999ee 0%, #00f2fe 100%);
    }

    &.open-status {
      background: linear-gradient(135deg, #efa891 0%, #ffecd2 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.tat-status {
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.resolved-status {
      background: linear-gradient(135deg, #79d2cf 0%, #f9c7d7 100%);

      h2,
      p {
        color: #383535;
      }
    }

    &.closed-status {
      background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    }
  }
}

// MyFeedback specific styles
.my-feedback {
  padding: 0;
}

.loading {
  @include flex-center;
  min-height: 200px;
  font-size: 14px;
  color: $text-secondary;
}

// Modern Creative Table Design
.modern-table-container {
  background: $white;
  border-radius: 1.5rem;
  overflow: hidden;
  @include card-shadow;
  margin: 1.5rem 0;
  border: 1px solid $border-color;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $primary-color 0%, $secondary-color 50%, $info-color 100%);
    z-index: 1;
  }
}

.table-responsive {
  border-radius: 1.5rem;
  overflow: hidden;
  background: $white;
  margin: 0;
  padding: 0;

  .table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
    background: $white;
    border-radius: 1.5rem;
    overflow: hidden;
    font-family: 'Roboto', sans-serif;

    &.table-custom {
      box-shadow: none;
      border: none;
    }
  }
}

// Modern Table Header Design
.table thead {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, $primary-color 0%, $secondary-color 50%, $info-color 100%);
  }

  th {
    background: transparent !important;
    color: $white !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 1.25rem 1rem !important;
    border: none !important;
    position: relative;

    &:first-child {
      padding-left: 1.5rem !important;
    }

    &:last-child {
      padding-right: 1.5rem !important;
    }

    // Add subtle separators between headers
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 25%;
      bottom: 25%;
      width: 1px;
      background: rgba($white, 0.2);
    }
  }
}

// Modern Table Body Design
.table tbody {
  tr {
    @include transition-smooth;
    border: none !important;
    background: $white;

    &:nth-child(even) {
      background: #f8fafc;
    }

    &:hover {
      background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($info-color, 0.05) 100%) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

      td {
        border-color: rgba($primary-color, 0.2) !important;
      }
    }

    &:last-child td {
      border-bottom: none !important;
    }
  }

  td {
    padding: 1rem !important;
    border: none !important;
    border-bottom: 1px solid #e2e8f0 !important;
    font-size: 0.875rem !important;
    color: $text-primary !important;
    vertical-align: middle !important;
    @include transition-smooth;

    &:first-child {
      padding-left: 1.5rem !important;
      font-weight: 600;
      color: $primary-color;
    }

    &:last-child {
      padding-right: 1.5rem !important;
    }
  }
}

// Enhanced Feedback Link Styling
.feedback-link {
  color: $primary-color !important;
  text-decoration: none !important;
  font-weight: 600 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  background: linear-gradient(135deg, rgba($primary-color, 0.1) 0%, rgba($info-color, 0.1) 100%) !important;
  border: 1px solid rgba($primary-color, 0.2) !important;
  display: inline-block !important;
  @include transition-smooth;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba($white, 0.4), transparent);
    transition: left 0.5s;
  }

  &:hover {
    color: $white !important;
    background: linear-gradient(135deg, $primary-color 0%, $info-color 100%) !important;
    border-color: $primary-color !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba($primary-color, 0.3);
    text-decoration: none !important;

    &::before {
      left: 100%;
    }
  }

  &:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3) !important;
  }
}

// Status Badge Styling
.status-badge {
  padding: 0.375rem 0.75rem !important;
  border-radius: 1rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  @include transition-smooth;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
  }

  // Status-specific colors
  &.status-new {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-open {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-resolved {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-closed {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }

  &.status-pending {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: $white;

    &::before {
      background: $white;
    }
  }
}

// Enhanced No Records Message
.no-records {
  text-align: center !important;
  padding: 4rem 2rem !important;
  color: $text-secondary !important;
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-radius: 1rem !important;
  margin: 2rem !important;
  border: 2px dashed $border-color !important;
  position: relative !important;

  &::before {
    content: '📋';
    font-size: 3rem;
    display: block;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  &::after {
    content: 'Try adjusting your search criteria or create a new record.';
    display: block;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    opacity: 0.7;
  }
}

// Table Loading Animation
.table-loading {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($white, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
}

// Responsive Table Design
@media (max-width: 1200px) {
  .table thead th {
    padding: 1rem 0.75rem !important;
    font-size: 0.8rem !important;
  }

  .table tbody td {
    padding: 0.875rem 0.75rem !important;
    font-size: 0.8rem !important;
  }
}

@media (max-width: 768px) {
  .modern-table-container {
    margin: 1rem 0;
    border-radius: 1rem;
  }

  .table-responsive {
    border-radius: 1rem;

    .table {
      border-radius: 1rem;
      min-width: 600px; // Ensure horizontal scroll on mobile
    }
  }

  .table thead th {
    padding: 0.875rem 0.5rem !important;
    font-size: 0.75rem !important;

    &:first-child {
      padding-left: 1rem !important;
    }

    &:last-child {
      padding-right: 1rem !important;
    }
  }

  .table tbody td {
    padding: 0.75rem 0.5rem !important;
    font-size: 0.75rem !important;

    &:first-child {
      padding-left: 1rem !important;
    }

    &:last-child {
      padding-right: 1rem !important;
    }
  }

  .feedback-link {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .status-badge {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
  }
}

// CreateFeedback Header Styles
.feedback-header {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%) !important;
  border-radius: 1rem !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
  position: relative;
  overflow: hidden;
  color: $white !important;
  @include card-shadow;

  // Decorative Elements
  .feedback-header-decoration-1 {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba($white, 0.1);
    z-index: 1;
  }

  .feedback-header-decoration-2 {
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba($white, 0.05);
    z-index: 1;
  }

  .feedback-header-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .feedback-header-icon {
    font-size: 3rem;
    color: $white !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba($white, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);

    svg {
      font-size: 2.5rem !important;
      color: $white !important;
    }
  }

  .feedback-header-text {
    flex: 1;
  }

  .feedback-header-title {
    font-weight: 700 !important;
    color: $white !important;
    margin-bottom: 0.5rem !important;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .feedback-header-badge {
    background: rgba($white, 0.2) !important;
    color: $white !important;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .feedback-header-subtitle {
    color: rgba($white, 0.9) !important;
    margin-bottom: 1rem !important;
  }

  .feedback-header-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
  }

  .feedback-quick-report-btn,
  .feedback-support-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid rgba($white, 0.3) !important;
    background: rgba($white, 0.1) !important;
    color: $white !important;
    text-decoration: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    @include transition-smooth;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba($white, 0.2) !important;
      border-color: rgba($white, 0.5) !important;
      transform: translateY(-2px);
      color: $white !important;
      text-decoration: none;
    }

    svg {
      font-size: 1rem !important;
      color: $white !important;
    }
  }

  .feedback-quick-report-btn {
    cursor: pointer;
    border: none;
  }

  // Additional feedback header overrides
  * {
    color: $white !important;
  }

  .MuiTypography-root {
    color: $white !important;
  }

  .MuiSvgIcon-root {
    color: $white !important;
  }
}










